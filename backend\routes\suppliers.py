from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt
from app import db
from models.supplier import Supplier
from models.audit_log import AuditLog

suppliers_bp = Blueprint('suppliers', __name__)

@suppliers_bp.route('/', methods=['GET'])
@jwt_required()
def get_suppliers():
    """Get all suppliers"""
    try:
        claims = get_jwt()
        branch_id = claims.get('branch_id')
        
        # Build query with branch filter if applicable
        query = Supplier.query
        if branch_id:
            query = query.filter_by(branch_id=branch_id)
        
        # Apply filters
        status = request.args.get('status')
        if status:
            query = query.filter_by(status=status)
        
        search = request.args.get('search')
        if search:
            query = query.filter(
                (Supplier.name.contains(search)) |
                (Supplier.code.contains(search)) |
                (Supplier.email.contains(search))
            )
        
        # Pagination
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        suppliers = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'suppliers': [supplier.to_dict() for supplier in suppliers.items],
            'total': suppliers.total,
            'pages': suppliers.pages,
            'current_page': page
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@suppliers_bp.route('/<int:supplier_id>', methods=['GET'])
@jwt_required()
def get_supplier(supplier_id):
    """Get specific supplier"""
    try:
        supplier = Supplier.query.get_or_404(supplier_id)
        return jsonify({'supplier': supplier.to_dict()}), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@suppliers_bp.route('/', methods=['POST'])
@jwt_required()
def create_supplier():
    """Create new supplier"""
    try:
        current_user_id = get_jwt_identity()
        claims = get_jwt()
        branch_id = claims.get('branch_id')
        
        data = request.get_json()
        
        # Validate required fields
        if not data.get('name') or not data.get('code'):
            return jsonify({'error': 'Name and code are required'}), 400
        
        # Check if code already exists
        existing = Supplier.query.filter_by(code=data['code']).first()
        if existing:
            return jsonify({'error': 'Supplier code already exists'}), 400
        
        # Create supplier
        supplier = Supplier(
            code=data['code'],
            name=data['name'],
            name_ar=data.get('name_ar'),
            contact_person=data.get('contact_person'),
            email=data.get('email'),
            phone=data.get('phone'),
            mobile=data.get('mobile'),
            address=data.get('address'),
            city=data.get('city'),
            country=data.get('country'),
            branch_id=branch_id
        )
        
        db.session.add(supplier)
        db.session.commit()
        
        # Log action
        AuditLog.log_action(
            user_id=current_user_id,
            action='create',
            resource_type='supplier',
            resource_id=supplier.id,
            details=f'Created supplier: {supplier.name}',
            ip_address=request.remote_addr,
            branch_id=branch_id
        )
        db.session.commit()
        
        return jsonify({
            'message': 'Supplier created successfully',
            'supplier': supplier.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# Add more routes for update, delete, etc.
# This is a basic implementation - you would expand with full CRUD operations
