from app import db
from datetime import datetime

class Branch(db.Model):
    __tablename__ = 'branches'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    code = db.Column(db.String(20), unique=True, nullable=False)
    
    # Contact information
    address = db.Column(db.Text)
    city = db.Column(db.String(50))
    state = db.Column(db.String(50))
    postal_code = db.Column(db.String(20))
    country = db.Column(db.String(50))
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    fax = db.Column(db.String(20))
    
    # Business information
    manager_name = db.Column(db.String(100))
    tax_id = db.Column(db.String(50))
    registration_number = db.Column(db.String(50))
    
    # Settings
    currency = db.Column(db.String(3), default='USD')
    timezone = db.Column(db.String(50), default='UTC')
    language = db.Column(db.String(5), default='en')
    
    # Status
    is_active = db.Column(db.Boolean, default=True)
    is_main_branch = db.Column(db.Boolean, default=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        """Convert branch object to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'code': self.code,
            'address': self.address,
            'city': self.city,
            'state': self.state,
            'postal_code': self.postal_code,
            'country': self.country,
            'phone': self.phone,
            'email': self.email,
            'fax': self.fax,
            'manager_name': self.manager_name,
            'tax_id': self.tax_id,
            'registration_number': self.registration_number,
            'currency': self.currency,
            'timezone': self.timezone,
            'language': self.language,
            'is_active': self.is_active,
            'is_main_branch': self.is_main_branch,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def __repr__(self):
        return f'<Branch {self.name}>'
