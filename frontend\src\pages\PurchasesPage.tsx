import React from 'react';
import { Box, Typography, Paper } from '@mui/material';
import { useTranslation } from 'react-i18next';

const PurchasesPage: React.FC = () => {
  const { t } = useTranslation();

  return (
    <Box>
      <Typography variant="h4" gutterBottom fontWeight="bold">
        {t('purchases')}
      </Typography>
      <Paper sx={{ p: 3 }}>
        <Typography variant="body1">
          Purchase management functionality will be implemented here.
        </Typography>
      </Paper>
    </Box>
  );
};

export default PurchasesPage;
