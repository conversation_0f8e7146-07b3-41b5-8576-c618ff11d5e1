import React from 'react';
import { Box, Typography, Paper } from '@mui/material';
import { useTranslation } from 'react-i18next';

const EmployeesPage: React.FC = () => {
  const { t } = useTranslation();

  return (
    <Box>
      <Typography variant="h4" gutterBottom fontWeight="bold">
        {t('employees')}
      </Typography>
      <Paper sx={{ p: 3 }}>
        <Typography variant="body1">
          Employee management functionality will be implemented here.
        </Typography>
      </Paper>
    </Box>
  );
};

export default EmployeesPage;
