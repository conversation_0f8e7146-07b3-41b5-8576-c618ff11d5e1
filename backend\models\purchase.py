from app import db
from datetime import datetime

class Purchase(db.Model):
    __tablename__ = 'purchases'
    
    id = db.Column(db.Integer, primary_key=True)
    purchase_number = db.Column(db.String(50), unique=True, nullable=False)
    
    # Supplier information
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=False)
    
    # Purchase details
    purchase_date = db.Column(db.DateTime, default=datetime.utcnow)
    expected_delivery_date = db.Column(db.DateTime)
    actual_delivery_date = db.Column(db.DateTime)
    
    # Financial information
    subtotal = db.Column(db.Numeric(15, 2), default=0)
    tax_amount = db.Column(db.Numeric(15, 2), default=0)
    discount_amount = db.Column(db.Numeric(15, 2), default=0)
    shipping_cost = db.Column(db.Numeric(15, 2), default=0)
    total_amount = db.Column(db.Numeric(15, 2), default=0)
    paid_amount = db.Column(db.Numeric(15, 2), default=0)
    balance_due = db.Column(db.Numeric(15, 2), default=0)
    
    # Payment information
    payment_terms = db.Column(db.String(50))  # Net 30, Net 60, etc.
    payment_status = db.Column(db.String(20), default='pending')  # pending, partial, paid
    
    # Status
    status = db.Column(db.String(20), default='draft')  # draft, ordered, received, cancelled
    
    # Reference information
    supplier_invoice_number = db.Column(db.String(50))
    reference_number = db.Column(db.String(50))
    
    # Additional information
    notes = db.Column(db.Text)
    internal_notes = db.Column(db.Text)
    
    # Staff information
    buyer_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # Branch association
    branch_id = db.Column(db.Integer, db.ForeignKey('branches.id'))
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    branch = db.relationship('Branch', backref='purchases')
    buyer = db.relationship('User', backref='purchases')
    items = db.relationship('PurchaseItem', backref='purchase', lazy='dynamic', cascade='all, delete-orphan')
    
    def calculate_totals(self):
        """Calculate purchase totals from items"""
        subtotal = sum(item.total_amount for item in self.items)
        self.subtotal = subtotal
        
        # Calculate tax (simplified - could be more complex)
        self.tax_amount = subtotal * 0.1  # 10% tax rate
        
        # Calculate total
        self.total_amount = subtotal + self.tax_amount - self.discount_amount + self.shipping_cost
        self.balance_due = self.total_amount - self.paid_amount
        
        db.session.commit()
    
    def add_payment(self, amount, notes=None):
        """Add payment to purchase"""
        if amount <= 0:
            return False
        
        self.paid_amount += amount
        self.balance_due = self.total_amount - self.paid_amount
        
        if self.balance_due <= 0:
            self.payment_status = 'paid'
        elif self.paid_amount > 0:
            self.payment_status = 'partial'
        
        db.session.commit()
        return True
    
    def receive_items(self):
        """Mark purchase as received and update inventory"""
        if self.status != 'ordered':
            return False
        
        self.status = 'received'
        self.actual_delivery_date = datetime.utcnow()
        
        # Update inventory for each item
        for item in self.items:
            if item.product and item.product.track_inventory:
                inventory = item.product.inventory
                if inventory:
                    inventory.add_stock(
                        quantity=item.quantity_received or item.quantity,
                        cost=item.unit_cost,
                        reference=f'Purchase {self.purchase_number}',
                        notes=f'Received from {self.supplier.name}'
                    )
        
        db.session.commit()
        return True
    
    def to_dict(self):
        """Convert purchase object to dictionary"""
        return {
            'id': self.id,
            'purchase_number': self.purchase_number,
            'supplier_id': self.supplier_id,
            'purchase_date': self.purchase_date.isoformat(),
            'expected_delivery_date': self.expected_delivery_date.isoformat() if self.expected_delivery_date else None,
            'actual_delivery_date': self.actual_delivery_date.isoformat() if self.actual_delivery_date else None,
            'subtotal': float(self.subtotal) if self.subtotal else 0,
            'tax_amount': float(self.tax_amount) if self.tax_amount else 0,
            'discount_amount': float(self.discount_amount) if self.discount_amount else 0,
            'shipping_cost': float(self.shipping_cost) if self.shipping_cost else 0,
            'total_amount': float(self.total_amount) if self.total_amount else 0,
            'paid_amount': float(self.paid_amount) if self.paid_amount else 0,
            'balance_due': float(self.balance_due) if self.balance_due else 0,
            'payment_terms': self.payment_terms,
            'payment_status': self.payment_status,
            'status': self.status,
            'supplier_invoice_number': self.supplier_invoice_number,
            'reference_number': self.reference_number,
            'notes': self.notes,
            'internal_notes': self.internal_notes,
            'buyer_id': self.buyer_id,
            'branch_id': self.branch_id,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'items': [item.to_dict() for item in self.items]
        }
    
    def __repr__(self):
        return f'<Purchase {self.purchase_number}>'

class PurchaseItem(db.Model):
    __tablename__ = 'purchase_items'
    
    id = db.Column(db.Integer, primary_key=True)
    purchase_id = db.Column(db.Integer, db.ForeignKey('purchases.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False)
    
    # Item details
    product_name = db.Column(db.String(100), nullable=False)  # Snapshot of product name
    product_code = db.Column(db.String(50))  # Snapshot of product code
    
    # Quantities and pricing
    quantity = db.Column(db.Integer, nullable=False, default=1)
    quantity_received = db.Column(db.Integer, default=0)
    unit_cost = db.Column(db.Numeric(15, 2), nullable=False)
    discount_percentage = db.Column(db.Numeric(5, 2), default=0)
    discount_amount = db.Column(db.Numeric(15, 2), default=0)
    tax_rate = db.Column(db.Numeric(5, 2), default=0)
    tax_amount = db.Column(db.Numeric(15, 2), default=0)
    total_amount = db.Column(db.Numeric(15, 2), nullable=False)
    
    # Additional information
    notes = db.Column(db.Text)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    product = db.relationship('Product', backref='purchase_items')
    
    def calculate_total(self):
        """Calculate item total"""
        subtotal = self.quantity * self.unit_cost
        discount = subtotal * (self.discount_percentage / 100) if self.discount_percentage else self.discount_amount
        taxable_amount = subtotal - discount
        tax = taxable_amount * (self.tax_rate / 100)
        self.total_amount = taxable_amount + tax
        return self.total_amount
    
    def to_dict(self):
        """Convert purchase item object to dictionary"""
        return {
            'id': self.id,
            'purchase_id': self.purchase_id,
            'product_id': self.product_id,
            'product_name': self.product_name,
            'product_code': self.product_code,
            'quantity': self.quantity,
            'quantity_received': self.quantity_received,
            'unit_cost': float(self.unit_cost) if self.unit_cost else 0,
            'discount_percentage': float(self.discount_percentage) if self.discount_percentage else 0,
            'discount_amount': float(self.discount_amount) if self.discount_amount else 0,
            'tax_rate': float(self.tax_rate) if self.tax_rate else 0,
            'tax_amount': float(self.tax_amount) if self.tax_amount else 0,
            'total_amount': float(self.total_amount) if self.total_amount else 0,
            'notes': self.notes,
            'created_at': self.created_at.isoformat()
        }
    
    def __repr__(self):
        return f'<PurchaseItem {self.product_name}: {self.quantity}>'
