# Project Structure

```
business-management-system/
├── backend/
│   ├── app.py                      # Main Flask application
│   ├── config.py                   # Configuration settings
│   ├── requirements.txt            # Python dependencies
│   ├── models/
│   │   ├── __init__.py
│   │   ├── user.py                 # User model
│   │   ├── supplier.py             # Supplier model
│   │   ├── customer.py             # Customer model
│   │   ├── product.py              # Product model
│   │   ├── purchase.py             # Purchase model
│   │   ├── sale.py                 # Sale model
│   │   ├── inventory.py            # Inventory model
│   │   ├── invoice.py              # Invoice model
│   │   ├── employee.py             # Employee model
│   │   ├── account.py              # Accounting model
│   │   ├── branch.py               # Branch model
│   │   └── audit_log.py            # Audit log model
│   ├── routes/
│   │   ├── __init__.py
│   │   ├── auth.py                 # Authentication routes
│   │   ├── purchases.py            # Purchase management routes
│   │   ├── suppliers.py            # Supplier management routes
│   │   ├── customers.py            # Customer management routes
│   │   ├── sales.py                # Sales management routes
│   │   ├── inventory.py            # Inventory management routes
│   │   ├── accounting.py           # Accounting routes
│   │   ├── reports.py              # Financial reports routes
│   │   ├── invoices.py             # Invoice management routes
│   │   ├── employees.py            # Employee management routes
│   │   ├── notifications.py        # Notification routes
│   │   ├── audit.py                # Audit log routes
│   │   ├── backup.py               # Backup routes
│   │   ├── settings.py             # System settings routes
│   │   ├── branches.py             # Branch management routes
│   │   └── dashboard.py            # Dashboard routes
│   ├── services/
│   │   ├── __init__.py
│   │   ├── auth_service.py         # Authentication service
│   │   ├── email_service.py        # Email service
│   │   ├── backup_service.py       # Backup service
│   │   ├── notification_service.py # Notification service
│   │   └── report_service.py       # Report generation service
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── decorators.py           # Custom decorators
│   │   ├── validators.py           # Input validators
│   │   └── helpers.py              # Helper functions
│   ├── migrations/
│   │   └── init_db.py              # Database initialization
│   ├── uploads/                    # File uploads directory
│   │   ├── invoices/
│   │   ├── documents/
│   │   └── images/
│   └── database.db                 # SQLite database file
├── frontend/
│   ├── public/
│   │   ├── index.html
│   │   ├── favicon.ico
│   │   └── locales/
│   │       ├── ar/
│   │       │   └── translation.json
│   │       └── en/
│   │           └── translation.json
│   ├── src/
│   │   ├── components/
│   │   │   ├── common/
│   │   │   │   ├── Header.tsx
│   │   │   │   ├── Sidebar.tsx
│   │   │   │   ├── Footer.tsx
│   │   │   │   ├── LoadingSpinner.tsx
│   │   │   │   └── ConfirmDialog.tsx
│   │   │   ├── auth/
│   │   │   │   ├── LoginForm.tsx
│   │   │   │   └── ProtectedRoute.tsx
│   │   │   ├── dashboard/
│   │   │   │   ├── Dashboard.tsx
│   │   │   │   ├── KPICard.tsx
│   │   │   │   └── Charts.tsx
│   │   │   ├── purchases/
│   │   │   │   ├── PurchaseList.tsx
│   │   │   │   ├── PurchaseForm.tsx
│   │   │   │   └── PurchaseDetails.tsx
│   │   │   ├── suppliers/
│   │   │   │   ├── SupplierList.tsx
│   │   │   │   ├── SupplierForm.tsx
│   │   │   │   └── SupplierDetails.tsx
│   │   │   ├── customers/
│   │   │   │   ├── CustomerList.tsx
│   │   │   │   ├── CustomerForm.tsx
│   │   │   │   └── CustomerDetails.tsx
│   │   │   ├── sales/
│   │   │   │   ├── SalesList.tsx
│   │   │   │   ├── SalesForm.tsx
│   │   │   │   └── POS.tsx
│   │   │   ├── inventory/
│   │   │   │   ├── InventoryList.tsx
│   │   │   │   ├── ProductForm.tsx
│   │   │   │   └── StockMovements.tsx
│   │   │   ├── accounting/
│   │   │   │   ├── ChartOfAccounts.tsx
│   │   │   │   ├── JournalEntries.tsx
│   │   │   │   └── GeneralLedger.tsx
│   │   │   ├── reports/
│   │   │   │   ├── FinancialReports.tsx
│   │   │   │   ├── ProfitLoss.tsx
│   │   │   │   └── BalanceSheet.tsx
│   │   │   ├── invoices/
│   │   │   │   ├── InvoiceList.tsx
│   │   │   │   ├── InvoiceForm.tsx
│   │   │   │   └── InvoicePreview.tsx
│   │   │   ├── employees/
│   │   │   │   ├── EmployeeList.tsx
│   │   │   │   ├── EmployeeForm.tsx
│   │   │   │   └── PayrollManagement.tsx
│   │   │   ├── settings/
│   │   │   │   ├── SystemSettings.tsx
│   │   │   │   ├── UserManagement.tsx
│   │   │   │   └── BranchManagement.tsx
│   │   │   └── notifications/
│   │   │       ├── NotificationCenter.tsx
│   │   │       └── NotificationItem.tsx
│   │   ├── pages/
│   │   │   ├── LoginPage.tsx
│   │   │   ├── DashboardPage.tsx
│   │   │   ├── PurchasesPage.tsx
│   │   │   ├── SuppliersPage.tsx
│   │   │   ├── CustomersPage.tsx
│   │   │   ├── SalesPage.tsx
│   │   │   ├── InventoryPage.tsx
│   │   │   ├── AccountingPage.tsx
│   │   │   ├── ReportsPage.tsx
│   │   │   ├── InvoicesPage.tsx
│   │   │   ├── EmployeesPage.tsx
│   │   │   ├── SettingsPage.tsx
│   │   │   └── NotFoundPage.tsx
│   │   ├── hooks/
│   │   │   ├── useAuth.ts
│   │   │   ├── useApi.ts
│   │   │   └── useNotifications.ts
│   │   ├── services/
│   │   │   ├── api.ts
│   │   │   ├── auth.ts
│   │   │   └── storage.ts
│   │   ├── utils/
│   │   │   ├── constants.ts
│   │   │   ├── formatters.ts
│   │   │   └── validators.ts
│   │   ├── types/
│   │   │   ├── auth.ts
│   │   │   ├── api.ts
│   │   │   └── models.ts
│   │   ├── styles/
│   │   │   ├── theme.ts
│   │   │   └── globals.css
│   │   ├── App.tsx
│   │   ├── main.tsx
│   │   └── i18n.ts
│   ├── package.json
│   ├── tsconfig.json
│   ├── vite.config.ts
│   └── .eslintrc.js
├── docs/
│   ├── api-documentation.md
│   ├── user-manual-ar.md
│   ├── user-manual-en.md
│   └── deployment-guide.md
├── tests/
│   ├── backend/
│   │   ├── test_auth.py
│   │   ├── test_models.py
│   │   └── test_routes.py
│   └── frontend/
│       ├── components/
│       └── pages/
└── docker/
    ├── Dockerfile.backend
    ├── Dockerfile.frontend
    └── docker-compose.yml
```
