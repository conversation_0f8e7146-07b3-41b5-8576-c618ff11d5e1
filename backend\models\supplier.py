from app import db
from datetime import datetime

class Supplier(db.Model):
    __tablename__ = 'suppliers'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(20), unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=False)
    name_ar = db.Column(db.String(100))  # Arabic name
    
    # Contact information
    contact_person = db.Column(db.String(100))
    email = db.Column(db.String(120))
    phone = db.Column(db.String(20))
    mobile = db.Column(db.String(20))
    fax = db.Column(db.String(20))
    website = db.Column(db.String(255))
    
    # Address
    address = db.Column(db.Text)
    city = db.Column(db.String(50))
    state = db.Column(db.String(50))
    postal_code = db.Column(db.String(20))
    country = db.Column(db.String(50))
    
    # Business information
    tax_id = db.Column(db.String(50))
    registration_number = db.Column(db.String(50))
    business_type = db.Column(db.String(50))
    industry = db.Column(db.String(50))
    
    # Financial information
    credit_limit = db.Column(db.Numeric(15, 2), default=0)
    payment_terms = db.Column(db.String(50))  # Net 30, Net 60, etc.
    currency = db.Column(db.String(3), default='USD')
    
    # Banking information
    bank_name = db.Column(db.String(100))
    bank_account = db.Column(db.String(50))
    iban = db.Column(db.String(50))
    swift_code = db.Column(db.String(20))
    
    # Rating and status
    rating = db.Column(db.Integer, default=5)  # 1-10 scale
    status = db.Column(db.String(20), default='active')  # active, inactive, blocked
    is_preferred = db.Column(db.Boolean, default=False)
    
    # Additional information
    notes = db.Column(db.Text)
    tags = db.Column(db.String(255))  # Comma-separated tags
    
    # Branch association
    branch_id = db.Column(db.Integer, db.ForeignKey('branches.id'))
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    branch = db.relationship('Branch', backref='suppliers')
    purchases = db.relationship('Purchase', backref='supplier', lazy='dynamic')
    
    def calculate_total_purchases(self):
        """Calculate total purchase amount from this supplier"""
        total = db.session.query(db.func.sum(Purchase.total_amount)).filter_by(supplier_id=self.id).scalar()
        return float(total) if total else 0.0
    
    def calculate_outstanding_balance(self):
        """Calculate outstanding balance with this supplier"""
        # This would be calculated based on unpaid invoices
        # For now, returning 0 as placeholder
        return 0.0
    
    def get_recent_purchases(self, limit=5):
        """Get recent purchases from this supplier"""
        return self.purchases.order_by(Purchase.created_at.desc()).limit(limit).all()
    
    def to_dict(self):
        """Convert supplier object to dictionary"""
        return {
            'id': self.id,
            'code': self.code,
            'name': self.name,
            'name_ar': self.name_ar,
            'contact_person': self.contact_person,
            'email': self.email,
            'phone': self.phone,
            'mobile': self.mobile,
            'fax': self.fax,
            'website': self.website,
            'address': self.address,
            'city': self.city,
            'state': self.state,
            'postal_code': self.postal_code,
            'country': self.country,
            'tax_id': self.tax_id,
            'registration_number': self.registration_number,
            'business_type': self.business_type,
            'industry': self.industry,
            'credit_limit': float(self.credit_limit) if self.credit_limit else 0,
            'payment_terms': self.payment_terms,
            'currency': self.currency,
            'bank_name': self.bank_name,
            'bank_account': self.bank_account,
            'iban': self.iban,
            'swift_code': self.swift_code,
            'rating': self.rating,
            'status': self.status,
            'is_preferred': self.is_preferred,
            'notes': self.notes,
            'tags': self.tags,
            'branch_id': self.branch_id,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'total_purchases': self.calculate_total_purchases(),
            'outstanding_balance': self.calculate_outstanding_balance()
        }
    
    def __repr__(self):
        return f'<Supplier {self.name}>'
