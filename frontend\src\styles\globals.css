/* Global styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Cairo', 'Roboto', 'Helvetica', 'Arial', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .MuiDrawer-paper {
  right: 0;
  left: auto;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Loading animation */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* Custom classes for Arabic text */
.arabic-text {
  font-family: 'Cairo', sans-serif;
  direction: rtl;
  text-align: right;
}

.english-text {
  font-family: 'Roboto', sans-serif;
  direction: ltr;
  text-align: left;
}

/* Dashboard cards */
.dashboard-card {
  transition: transform 0.2s ease-in-out;
}

.dashboard-card:hover {
  transform: translateY(-2px);
}

/* Form styles */
.form-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
}

/* Table styles */
.data-table {
  background: white;
  border-radius: 12px;
  overflow: hidden;
}

/* Notification styles */
.notification-success {
  background-color: #4caf50;
  color: white;
}

.notification-error {
  background-color: #f44336;
  color: white;
}

.notification-warning {
  background-color: #ff9800;
  color: white;
}

.notification-info {
  background-color: #2196f3;
  color: white;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }
  
  .mobile-full-width {
    width: 100%;
  }
}
