// Common interfaces for business models

export interface BaseModel {
  id: number;
  created_at: string;
  updated_at: string;
}

export interface Branch extends BaseModel {
  name: string;
  code: string;
  address?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  phone?: string;
  email?: string;
  manager_name?: string;
  currency: string;
  timezone: string;
  language: string;
  is_active: boolean;
  is_main_branch: boolean;
}

export interface Supplier extends BaseModel {
  code: string;
  name: string;
  name_ar?: string;
  contact_person?: string;
  email?: string;
  phone?: string;
  mobile?: string;
  address?: string;
  city?: string;
  country?: string;
  tax_id?: string;
  credit_limit: number;
  payment_terms?: string;
  currency: string;
  rating: number;
  status: 'active' | 'inactive' | 'blocked';
  is_preferred: boolean;
  branch_id?: number;
  total_purchases: number;
  outstanding_balance: number;
}

export interface Customer extends BaseModel {
  code: string;
  name: string;
  name_ar?: string;
  customer_type: 'individual' | 'company';
  contact_person?: string;
  email?: string;
  phone?: string;
  mobile?: string;
  address?: string;
  city?: string;
  country?: string;
  tax_id?: string;
  credit_limit: number;
  payment_terms?: string;
  currency: string;
  discount_percentage: number;
  category?: string;
  status: 'active' | 'inactive' | 'blocked';
  is_vip: boolean;
  preferred_language: string;
  branch_id?: number;
  total_sales: number;
  outstanding_balance: number;
  lifetime_value: number;
  last_purchase_date?: string;
}

export interface Product extends BaseModel {
  code: string;
  name: string;
  name_ar?: string;
  description?: string;
  description_ar?: string;
  category_id?: number;
  brand?: string;
  model?: string;
  cost_price: number;
  selling_price: number;
  wholesale_price: number;
  min_stock_level: number;
  max_stock_level: number;
  weight?: number;
  dimensions?: string;
  color?: string;
  size?: string;
  tax_rate: number;
  tax_inclusive: boolean;
  is_active: boolean;
  is_service: boolean;
  track_inventory: boolean;
  image_url?: string;
  barcode?: string;
  branch_id?: number;
  current_stock: number;
  profit_margin: number;
  is_low_stock: boolean;
  is_out_of_stock: boolean;
}

export interface Sale extends BaseModel {
  sale_number: string;
  customer_id?: number;
  customer_name?: string;
  sale_date: string;
  due_date?: string;
  subtotal: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  paid_amount: number;
  balance_due: number;
  payment_method?: string;
  payment_status: 'pending' | 'partial' | 'paid';
  status: 'draft' | 'confirmed' | 'delivered' | 'cancelled';
  sale_type: 'regular' | 'return' | 'exchange';
  notes?: string;
  salesperson_id?: number;
  branch_id?: number;
  items: SaleItem[];
}

export interface SaleItem {
  id: number;
  sale_id: number;
  product_id: number;
  product_name: string;
  product_code?: string;
  quantity: number;
  unit_price: number;
  discount_percentage: number;
  discount_amount: number;
  tax_rate: number;
  tax_amount: number;
  total_amount: number;
  notes?: string;
}

export interface Purchase extends BaseModel {
  purchase_number: string;
  supplier_id: number;
  purchase_date: string;
  expected_delivery_date?: string;
  actual_delivery_date?: string;
  subtotal: number;
  tax_amount: number;
  discount_amount: number;
  shipping_cost: number;
  total_amount: number;
  paid_amount: number;
  balance_due: number;
  payment_terms?: string;
  payment_status: 'pending' | 'partial' | 'paid';
  status: 'draft' | 'ordered' | 'received' | 'cancelled';
  supplier_invoice_number?: string;
  reference_number?: string;
  notes?: string;
  buyer_id?: number;
  branch_id?: number;
  items: PurchaseItem[];
}

export interface PurchaseItem {
  id: number;
  purchase_id: number;
  product_id: number;
  product_name: string;
  product_code?: string;
  quantity: number;
  quantity_received: number;
  unit_cost: number;
  discount_percentage: number;
  discount_amount: number;
  tax_rate: number;
  tax_amount: number;
  total_amount: number;
  notes?: string;
}

export interface Invoice extends BaseModel {
  invoice_number: string;
  customer_id: number;
  invoice_date: string;
  due_date?: string;
  subtotal: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  paid_amount: number;
  balance_due: number;
  status: 'draft' | 'sent' | 'pending' | 'paid' | 'overdue' | 'cancelled';
  payment_status: 'unpaid' | 'partial' | 'paid';
  reference_number?: string;
  sale_id?: number;
  payment_terms?: string;
  terms_and_conditions?: string;
  notes?: string;
  pdf_file_path?: string;
  created_by_id?: number;
  branch_id?: number;
  sent_at?: string;
  items: InvoiceItem[];
  payments: InvoicePayment[];
}

export interface InvoiceItem {
  id: number;
  invoice_id: number;
  product_id?: number;
  description: string;
  product_code?: string;
  quantity: number;
  unit_price: number;
  discount_percentage: number;
  discount_amount: number;
  tax_rate: number;
  tax_amount: number;
  total_amount: number;
}

export interface InvoicePayment {
  id: number;
  invoice_id: number;
  amount: number;
  payment_method: string;
  reference?: string;
  notes?: string;
  received_by_id?: number;
  branch_id?: number;
  created_at: string;
}

export interface Employee extends BaseModel {
  employee_number: string;
  first_name: string;
  last_name: string;
  first_name_ar?: string;
  last_name_ar?: string;
  email?: string;
  phone?: string;
  mobile?: string;
  address?: string;
  position?: string;
  department?: string;
  hire_date: string;
  employment_type: 'full_time' | 'part_time' | 'contract';
  salary: number;
  currency: string;
  is_active: boolean;
  branch_id?: number;
}
