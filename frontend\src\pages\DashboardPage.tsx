import React, { useEffect, useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  People,
  Business,
  Receipt,
  Inventory,
  Warning,
  CheckCircle,
  Error,
  Info,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { Line, Doughnut, Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  BarElement,
} from 'chart.js';
import { apiService } from '../services/api';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  BarElement
);

interface DashboardStats {
  sales: {
    today: number;
    week: number;
    month: number;
    year: number;
  };
  purchases: {
    month: number;
  };
  customers: {
    total: number;
    new_this_month: number;
  };
  suppliers: {
    total: number;
  };
  invoices: {
    pending: number;
    overdue: number;
    pending_amount: number;
  };
  inventory: {
    total_products: number;
    low_stock: number;
    out_of_stock: number;
  };
  profit: {
    month: number;
  };
}

interface Activity {
  type: string;
  title: string;
  description: string;
  amount: number;
  date: string;
  status: string;
}

interface Notification {
  type: 'warning' | 'error' | 'info' | 'success';
  title: string;
  message: string;
  action?: string;
  link?: string;
}

const DashboardPage: React.FC = () => {
  const { t, i18n } = useTranslation();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [activities, setActivities] = useState<Activity[]>([]);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [salesChartData, setSalesChartData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch dashboard stats
      const [statsResponse, activitiesResponse, notificationsResponse, chartResponse] = await Promise.all([
        apiService.get<DashboardStats>('/dashboard/stats'),
        apiService.get<{ activities: Activity[] }>('/dashboard/recent-activities'),
        apiService.get<{ notifications: Notification[] }>('/dashboard/notifications'),
        apiService.get('/dashboard/charts/sales'),
      ]);

      setStats(statsResponse);
      setActivities(activitiesResponse.activities);
      setNotifications(notificationsResponse.notifications);
      setSalesChartData(chartResponse);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(i18n.language === 'ar' ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'warning':
        return <Warning color="warning" />;
      case 'error':
        return <Error color="error" />;
      case 'success':
        return <CheckCircle color="success" />;
      default:
        return <Info color="info" />;
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'sale':
        return <TrendingUp color="success" />;
      case 'purchase':
        return <TrendingDown color="primary" />;
      case 'invoice':
        return <Receipt color="info" />;
      default:
        return <Info />;
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom fontWeight="bold">
        {t('dashboard')}
      </Typography>

      {/* Notifications */}
      {notifications.length > 0 && (
        <Box mb={3}>
          {notifications.map((notification, index) => (
            <Alert
              key={index}
              severity={notification.type}
              sx={{ mb: 1 }}
              action={
                notification.action && (
                  <Chip
                    label={notification.action}
                    size="small"
                    variant="outlined"
                    color={notification.type}
                  />
                )
              }
            >
              <strong>{notification.title}</strong> - {notification.message}
            </Alert>
          ))}
        </Box>
      )}

      {/* Stats Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <Card className="dashboard-card">
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="h6">
                    {t('totalSales')}
                  </Typography>
                  <Typography variant="h4" fontWeight="bold" color="success.main">
                    {formatCurrency(stats?.sales.month || 0)}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {i18n.language === 'ar' ? 'هذا الشهر' : 'This month'}
                  </Typography>
                </Box>
                <TrendingUp sx={{ fontSize: 40, color: 'success.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card className="dashboard-card">
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="h6">
                    {t('totalCustomers')}
                  </Typography>
                  <Typography variant="h4" fontWeight="bold" color="primary.main">
                    {stats?.customers.total || 0}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    +{stats?.customers.new_this_month || 0} {i18n.language === 'ar' ? 'جديد' : 'new'}
                  </Typography>
                </Box>
                <People sx={{ fontSize: 40, color: 'primary.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card className="dashboard-card">
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="h6">
                    {t('pendingInvoices')}
                  </Typography>
                  <Typography variant="h4" fontWeight="bold" color="warning.main">
                    {stats?.invoices.pending || 0}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {formatCurrency(stats?.invoices.pending_amount || 0)}
                  </Typography>
                </Box>
                <Receipt sx={{ fontSize: 40, color: 'warning.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card className="dashboard-card">
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="h6">
                    {t('lowStock')}
                  </Typography>
                  <Typography variant="h4" fontWeight="bold" color="error.main">
                    {stats?.inventory.low_stock || 0}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {i18n.language === 'ar' ? 'منتج' : 'products'}
                  </Typography>
                </Box>
                <Inventory sx={{ fontSize: 40, color: 'error.main' }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Sales Chart */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              {i18n.language === 'ar' ? 'مبيعات الـ 12 شهر الماضية' : 'Sales Last 12 Months'}
            </Typography>
            {salesChartData && (
              <Box height={300}>
                <Line
                  data={salesChartData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        position: 'top' as const,
                      },
                    },
                    scales: {
                      y: {
                        beginAtZero: true,
                      },
                    },
                  }}
                />
              </Box>
            )}
          </Paper>
        </Grid>

        {/* Recent Activities */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, height: 'fit-content' }}>
            <Typography variant="h6" gutterBottom>
              {t('recentActivities')}
            </Typography>
            <List>
              {activities.slice(0, 5).map((activity, index) => (
                <ListItem key={index} divider>
                  <ListItemIcon>
                    {getActivityIcon(activity.type)}
                  </ListItemIcon>
                  <ListItemText
                    primary={activity.title}
                    secondary={
                      <Box>
                        <Typography variant="body2" color="textSecondary">
                          {activity.description}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {new Date(activity.date).toLocaleDateString(
                            i18n.language === 'ar' ? 'ar-SA' : 'en-US'
                          )}
                        </Typography>
                      </Box>
                    }
                  />
                  <Typography variant="body2" fontWeight="bold" color="primary">
                    {formatCurrency(activity.amount)}
                  </Typography>
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* Quick Stats */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              {i18n.language === 'ar' ? 'إحصائيات سريعة' : 'Quick Stats'}
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Box textAlign="center" p={2}>
                  <Typography variant="h4" color="primary" fontWeight="bold">
                    {stats?.suppliers.total || 0}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {t('suppliers')}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box textAlign="center" p={2}>
                  <Typography variant="h4" color="success.main" fontWeight="bold">
                    {formatCurrency(stats?.profit.month || 0)}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {i18n.language === 'ar' ? 'الربح الشهري' : 'Monthly Profit'}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box textAlign="center" p={2}>
                  <Typography variant="h4" color="warning.main" fontWeight="bold">
                    {stats?.invoices.overdue || 0}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {i18n.language === 'ar' ? 'فواتير متأخرة' : 'Overdue Invoices'}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={6}>
                <Box textAlign="center" p={2}>
                  <Typography variant="h4" color="info.main" fontWeight="bold">
                    {stats?.inventory.total_products || 0}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {i18n.language === 'ar' ? 'إجمالي المنتجات' : 'Total Products'}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Today's Summary */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              {i18n.language === 'ar' ? 'ملخص اليوم' : "Today's Summary"}
            </Typography>
            <Box>
              <Box display="flex" justifyContent="space-between" alignItems="center" py={1}>
                <Typography variant="body1">
                  {i18n.language === 'ar' ? 'مبيعات اليوم' : "Today's Sales"}
                </Typography>
                <Typography variant="h6" color="success.main" fontWeight="bold">
                  {formatCurrency(stats?.sales.today || 0)}
                </Typography>
              </Box>
              <Box display="flex" justifyContent="space-between" alignItems="center" py={1}>
                <Typography variant="body1">
                  {i18n.language === 'ar' ? 'مبيعات الأسبوع' : "Week's Sales"}
                </Typography>
                <Typography variant="h6" color="primary.main" fontWeight="bold">
                  {formatCurrency(stats?.sales.week || 0)}
                </Typography>
              </Box>
              <Box display="flex" justifyContent="space-between" alignItems="center" py={1}>
                <Typography variant="body1">
                  {i18n.language === 'ar' ? 'مشتريات الشهر' : "Month's Purchases"}
                </Typography>
                <Typography variant="h6" color="warning.main" fontWeight="bold">
                  {formatCurrency(stats?.purchases.month || 0)}
                </Typography>
              </Box>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardPage;
