from app import db
from datetime import datetime

class Employee(db.Model):
    __tablename__ = 'employees'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    employee_number = db.Column(db.String(20), unique=True, nullable=False)
    
    # Personal information
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    first_name_ar = db.Column(db.String(50))
    last_name_ar = db.Column(db.String(50))
    
    # Contact information
    email = db.Column(db.String(120))
    phone = db.Column(db.String(20))
    mobile = db.Column(db.String(20))
    address = db.Column(db.Text)
    
    # Employment details
    position = db.Column(db.String(100))
    department = db.Column(db.String(100))
    hire_date = db.Column(db.DateTime, default=datetime.utcnow)
    employment_type = db.Column(db.String(20), default='full_time')  # full_time, part_time, contract
    
    # Salary information
    salary = db.Column(db.Numeric(15, 2))
    currency = db.Column(db.String(3), default='USD')
    
    # Status
    is_active = db.Column(db.Boolean, default=True)
    
    # Branch association
    branch_id = db.Column(db.Integer, db.ForeignKey('branches.id'))
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    branch = db.relationship('Branch', backref='employees')
    
    def to_dict(self):
        return {
            'id': self.id,
            'employee_number': self.employee_number,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'email': self.email,
            'phone': self.phone,
            'position': self.position,
            'department': self.department,
            'hire_date': self.hire_date.isoformat(),
            'employment_type': self.employment_type,
            'salary': float(self.salary) if self.salary else 0,
            'is_active': self.is_active,
            'branch_id': self.branch_id,
            'created_at': self.created_at.isoformat()
        }
    
    def __repr__(self):
        return f'<Employee {self.first_name} {self.last_name}>'
