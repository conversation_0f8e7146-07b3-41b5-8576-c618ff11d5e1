import { useState, useEffect, createContext, useContext } from 'react';
import { authService } from '../services/auth';
import { User } from '../types/auth';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
  updateProfile: (data: Partial<User>) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    // If no context provider, create a local auth state
    const [user, setUser] = useState<User | null>(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
      const initAuth = async () => {
        try {
          const token = localStorage.getItem('access_token');
          if (token) {
            const userData = await authService.verifyToken();
            setUser(userData);
          }
        } catch (error) {
          console.error('Auth initialization error:', error);
          localStorage.removeItem('access_token');
          localStorage.removeItem('refresh_token');
        } finally {
          setLoading(false);
        }
      };

      initAuth();
    }, []);

    const login = async (username: string, password: string) => {
      try {
        const response = await authService.login(username, password);
        setUser(response.user);
        localStorage.setItem('access_token', response.access_token);
        localStorage.setItem('refresh_token', response.refresh_token);
      } catch (error) {
        throw error;
      }
    };

    const logout = () => {
      authService.logout();
      setUser(null);
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
    };

    const updateProfile = async (data: Partial<User>) => {
      try {
        const updatedUser = await authService.updateProfile(data);
        setUser(updatedUser);
      } catch (error) {
        throw error;
      }
    };

    return {
      user,
      isAuthenticated: !!user,
      loading,
      login,
      logout,
      updateProfile,
    };
  }
  return context;
};
