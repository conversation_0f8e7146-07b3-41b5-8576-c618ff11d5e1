from app import db
from datetime import datetime

class Account(db.Model):
    __tablename__ = 'accounts'
    
    id = db.Column(db.<PERSON>te<PERSON>, primary_key=True)
    account_code = db.Column(db.String(20), unique=True, nullable=False)
    account_name = db.Column(db.String(100), nullable=False)
    account_name_ar = db.Column(db.String(100))
    
    # Account classification
    account_type = db.Column(db.String(20), nullable=False)  # asset, liability, equity, revenue, expense
    account_category = db.Column(db.String(50))  # current_asset, fixed_asset, etc.
    
    # Hierarchy
    parent_account_id = db.Column(db.Integer, db.<PERSON>ey('accounts.id'))
    
    # Properties
    is_active = db.Column(db.Boolean, default=True)
    is_system_account = db.Column(db.Boolean, default=False)
    
    # Balance tracking
    opening_balance = db.Column(db.Numeric(15, 2), default=0)
    current_balance = db.Column(db.Numeric(15, 2), default=0)
    
    # Branch association
    branch_id = db.Column(db.Integer, db.<PERSON>Key('branches.id'))
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    branch = db.relationship('Branch', backref='accounts')
    parent_account = db.relationship('Account', remote_side=[id], backref='sub_accounts')
    
    def to_dict(self):
        return {
            'id': self.id,
            'account_code': self.account_code,
            'account_name': self.account_name,
            'account_type': self.account_type,
            'account_category': self.account_category,
            'parent_account_id': self.parent_account_id,
            'is_active': self.is_active,
            'opening_balance': float(self.opening_balance) if self.opening_balance else 0,
            'current_balance': float(self.current_balance) if self.current_balance else 0,
            'branch_id': self.branch_id,
            'created_at': self.created_at.isoformat()
        }
    
    def __repr__(self):
        return f'<Account {self.account_code}: {self.account_name}>'
