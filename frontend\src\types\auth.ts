export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  phone?: string;
  avatar?: string;
  role: 'admin' | 'manager' | 'user' | 'viewer';
  branch_id?: number;
  is_active: boolean;
  is_verified: boolean;
  last_login?: string;
  two_factor_enabled: boolean;
  language: string;
  timezone: string;
  theme: string;
  created_at: string;
  updated_at: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  message: string;
  access_token: string;
  refresh_token: string;
  user: User;
}

export interface RefreshTokenResponse {
  access_token: string;
}

export interface ChangePasswordRequest {
  current_password: string;
  new_password: string;
}
