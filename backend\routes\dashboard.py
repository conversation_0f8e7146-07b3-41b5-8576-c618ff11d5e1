from flask import Blueprint, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt
from datetime import datetime, timedelta
from sqlalchemy import func, and_, or_
from app import db
from models.user import User
from models.customer import Customer
from models.supplier import Supplier
from models.sale import Sale
from models.purchase import Purchase
from models.invoice import Invoice
from models.product import Product
from models.inventory import Inventory

dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/stats', methods=['GET'])
@jwt_required()
def get_dashboard_stats():
    """Get main dashboard statistics"""
    try:
        current_user_id = get_jwt_identity()
        claims = get_jwt()
        branch_id = claims.get('branch_id')
        
        # Date ranges
        today = datetime.utcnow().date()
        week_start = today - timedelta(days=today.weekday())
        month_start = today.replace(day=1)
        year_start = today.replace(month=1, day=1)
        
        # Base query filters for branch
        branch_filter = {'branch_id': branch_id} if branch_id else {}
        
        # Sales statistics
        total_sales_today = db.session.query(func.sum(Sale.total_amount)).filter(
            func.date(Sale.created_at) == today,
            *[getattr(Sale, k) == v for k, v in branch_filter.items()]
        ).scalar() or 0
        
        total_sales_week = db.session.query(func.sum(Sale.total_amount)).filter(
            func.date(Sale.created_at) >= week_start,
            *[getattr(Sale, k) == v for k, v in branch_filter.items()]
        ).scalar() or 0
        
        total_sales_month = db.session.query(func.sum(Sale.total_amount)).filter(
            func.date(Sale.created_at) >= month_start,
            *[getattr(Sale, k) == v for k, v in branch_filter.items()]
        ).scalar() or 0
        
        total_sales_year = db.session.query(func.sum(Sale.total_amount)).filter(
            func.date(Sale.created_at) >= year_start,
            *[getattr(Sale, k) == v for k, v in branch_filter.items()]
        ).scalar() or 0
        
        # Purchase statistics
        total_purchases_month = db.session.query(func.sum(Purchase.total_amount)).filter(
            func.date(Purchase.created_at) >= month_start,
            *[getattr(Purchase, k) == v for k, v in branch_filter.items()]
        ).scalar() or 0
        
        # Customer statistics
        total_customers = Customer.query.filter_by(**branch_filter).count()
        new_customers_month = Customer.query.filter(
            func.date(Customer.created_at) >= month_start,
            *[getattr(Customer, k) == v for k, v in branch_filter.items()]
        ).count()
        
        # Supplier statistics
        total_suppliers = Supplier.query.filter_by(**branch_filter).count()
        
        # Invoice statistics
        pending_invoices = Invoice.query.filter(
            Invoice.status == 'pending',
            *[getattr(Invoice, k) == v for k, v in branch_filter.items()]
        ).count()
        
        overdue_invoices = Invoice.query.filter(
            Invoice.status == 'overdue',
            *[getattr(Invoice, k) == v for k, v in branch_filter.items()]
        ).count()
        
        total_pending_amount = db.session.query(func.sum(Invoice.total_amount)).filter(
            Invoice.status.in_(['pending', 'overdue']),
            *[getattr(Invoice, k) == v for k, v in branch_filter.items()]
        ).scalar() or 0
        
        # Inventory statistics
        total_products = Product.query.filter_by(**branch_filter).count()
        low_stock_products = db.session.query(Product).join(Inventory).filter(
            Inventory.quantity <= Product.min_stock_level,
            *[getattr(Product, k) == v for k, v in branch_filter.items()]
        ).count()
        
        out_of_stock_products = db.session.query(Product).join(Inventory).filter(
            Inventory.quantity == 0,
            *[getattr(Product, k) == v for k, v in branch_filter.items()]
        ).count()
        
        # Calculate profit (simplified - sales minus purchases)
        profit_month = float(total_sales_month) - float(total_purchases_month)
        
        return jsonify({
            'sales': {
                'today': float(total_sales_today),
                'week': float(total_sales_week),
                'month': float(total_sales_month),
                'year': float(total_sales_year)
            },
            'purchases': {
                'month': float(total_purchases_month)
            },
            'customers': {
                'total': total_customers,
                'new_this_month': new_customers_month
            },
            'suppliers': {
                'total': total_suppliers
            },
            'invoices': {
                'pending': pending_invoices,
                'overdue': overdue_invoices,
                'pending_amount': float(total_pending_amount)
            },
            'inventory': {
                'total_products': total_products,
                'low_stock': low_stock_products,
                'out_of_stock': out_of_stock_products
            },
            'profit': {
                'month': profit_month
            }
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@dashboard_bp.route('/recent-activities', methods=['GET'])
@jwt_required()
def get_recent_activities():
    """Get recent activities for dashboard"""
    try:
        current_user_id = get_jwt_identity()
        claims = get_jwt()
        branch_id = claims.get('branch_id')
        
        # Base query filters for branch
        branch_filter = {'branch_id': branch_id} if branch_id else {}
        
        activities = []
        
        # Recent sales
        recent_sales = Sale.query.filter_by(**branch_filter).order_by(
            Sale.created_at.desc()
        ).limit(5).all()
        
        for sale in recent_sales:
            activities.append({
                'type': 'sale',
                'title': f'Sale #{sale.id}',
                'description': f'Sale to {sale.customer.name if sale.customer else "Walk-in Customer"}',
                'amount': float(sale.total_amount),
                'date': sale.created_at.isoformat(),
                'status': sale.status
            })
        
        # Recent purchases
        recent_purchases = Purchase.query.filter_by(**branch_filter).order_by(
            Purchase.created_at.desc()
        ).limit(5).all()
        
        for purchase in recent_purchases:
            activities.append({
                'type': 'purchase',
                'title': f'Purchase #{purchase.id}',
                'description': f'Purchase from {purchase.supplier.name if purchase.supplier else "Unknown Supplier"}',
                'amount': float(purchase.total_amount),
                'date': purchase.created_at.isoformat(),
                'status': purchase.status
            })
        
        # Recent invoices
        recent_invoices = Invoice.query.filter_by(**branch_filter).order_by(
            Invoice.created_at.desc()
        ).limit(5).all()
        
        for invoice in recent_invoices:
            activities.append({
                'type': 'invoice',
                'title': f'Invoice #{invoice.invoice_number}',
                'description': f'Invoice for {invoice.customer.name if invoice.customer else "Unknown Customer"}',
                'amount': float(invoice.total_amount),
                'date': invoice.created_at.isoformat(),
                'status': invoice.status
            })
        
        # Sort activities by date
        activities.sort(key=lambda x: x['date'], reverse=True)
        
        return jsonify({
            'activities': activities[:10]  # Return top 10 most recent
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@dashboard_bp.route('/charts/sales', methods=['GET'])
@jwt_required()
def get_sales_chart_data():
    """Get sales chart data for dashboard"""
    try:
        current_user_id = get_jwt_identity()
        claims = get_jwt()
        branch_id = claims.get('branch_id')
        
        # Base query filters for branch
        branch_filter = {'branch_id': branch_id} if branch_id else {}
        
        # Get last 12 months of sales data
        end_date = datetime.utcnow().date()
        start_date = end_date - timedelta(days=365)
        
        # Monthly sales data
        monthly_sales = db.session.query(
            func.date_trunc('month', Sale.created_at).label('month'),
            func.sum(Sale.total_amount).label('total')
        ).filter(
            func.date(Sale.created_at) >= start_date,
            *[getattr(Sale, k) == v for k, v in branch_filter.items()]
        ).group_by(
            func.date_trunc('month', Sale.created_at)
        ).order_by('month').all()
        
        # Format data for chart
        chart_data = {
            'labels': [],
            'datasets': [{
                'label': 'Sales',
                'data': [],
                'backgroundColor': 'rgba(54, 162, 235, 0.2)',
                'borderColor': 'rgba(54, 162, 235, 1)',
                'borderWidth': 1
            }]
        }
        
        for month_data in monthly_sales:
            month_name = month_data.month.strftime('%b %Y')
            chart_data['labels'].append(month_name)
            chart_data['datasets'][0]['data'].append(float(month_data.total))
        
        return jsonify(chart_data), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@dashboard_bp.route('/charts/top-products', methods=['GET'])
@jwt_required()
def get_top_products_chart():
    """Get top selling products chart data"""
    try:
        current_user_id = get_jwt_identity()
        claims = get_jwt()
        branch_id = claims.get('branch_id')
        
        # This would require a SaleItem model to track individual products in sales
        # For now, returning placeholder data
        chart_data = {
            'labels': ['Product A', 'Product B', 'Product C', 'Product D', 'Product E'],
            'datasets': [{
                'label': 'Quantity Sold',
                'data': [120, 95, 80, 65, 45],
                'backgroundColor': [
                    'rgba(255, 99, 132, 0.2)',
                    'rgba(54, 162, 235, 0.2)',
                    'rgba(255, 205, 86, 0.2)',
                    'rgba(75, 192, 192, 0.2)',
                    'rgba(153, 102, 255, 0.2)'
                ],
                'borderColor': [
                    'rgba(255, 99, 132, 1)',
                    'rgba(54, 162, 235, 1)',
                    'rgba(255, 205, 86, 1)',
                    'rgba(75, 192, 192, 1)',
                    'rgba(153, 102, 255, 1)'
                ],
                'borderWidth': 1
            }]
        }
        
        return jsonify(chart_data), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@dashboard_bp.route('/notifications', methods=['GET'])
@jwt_required()
def get_dashboard_notifications():
    """Get important notifications for dashboard"""
    try:
        current_user_id = get_jwt_identity()
        claims = get_jwt()
        branch_id = claims.get('branch_id')
        
        notifications = []
        
        # Base query filters for branch
        branch_filter = {'branch_id': branch_id} if branch_id else {}
        
        # Low stock alerts
        low_stock_count = db.session.query(Product).join(Inventory).filter(
            Inventory.quantity <= Product.min_stock_level,
            *[getattr(Product, k) == v for k, v in branch_filter.items()]
        ).count()
        
        if low_stock_count > 0:
            notifications.append({
                'type': 'warning',
                'title': 'Low Stock Alert',
                'message': f'{low_stock_count} products are running low on stock',
                'action': 'View Inventory',
                'link': '/inventory'
            })
        
        # Overdue invoices
        overdue_count = Invoice.query.filter(
            Invoice.status == 'overdue',
            *[getattr(Invoice, k) == v for k, v in branch_filter.items()]
        ).count()
        
        if overdue_count > 0:
            notifications.append({
                'type': 'error',
                'title': 'Overdue Invoices',
                'message': f'{overdue_count} invoices are overdue',
                'action': 'View Invoices',
                'link': '/invoices'
            })
        
        # Pending invoices
        pending_count = Invoice.query.filter(
            Invoice.status == 'pending',
            *[getattr(Invoice, k) == v for k, v in branch_filter.items()]
        ).count()
        
        if pending_count > 0:
            notifications.append({
                'type': 'info',
                'title': 'Pending Invoices',
                'message': f'{pending_count} invoices are pending payment',
                'action': 'View Invoices',
                'link': '/invoices'
            })
        
        return jsonify({
            'notifications': notifications
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500
