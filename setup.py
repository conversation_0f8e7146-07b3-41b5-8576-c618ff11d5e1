#!/usr/bin/env python3
"""
Business Management System Setup Script
This script sets up the complete business management system with all dependencies.
"""

import os
import sys
import subprocess
import platform

def run_command(command, cwd=None):
    """Run a command and return the result"""
    try:
        result = subprocess.run(command, shell=True, cwd=cwd, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"Error running command: {command}")
            print(f"Error output: {result.stderr}")
            return False
        return True
    except Exception as e:
        print(f"Exception running command {command}: {e}")
        return False

def check_python_version():
    """Check if Python version is 3.8 or higher"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("Error: Python 3.8 or higher is required")
        return False
    print(f"✓ Python {version.major}.{version.minor}.{version.micro} detected")
    return True

def check_node_version():
    """Check if Node.js is installed"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✓ Node.js {version} detected")
            return True
    except FileNotFoundError:
        pass
    
    print("Error: Node.js is not installed or not in PATH")
    print("Please install Node.js 16 or higher from https://nodejs.org/")
    return False

def setup_backend():
    """Set up the Python backend"""
    print("\n🔧 Setting up Python backend...")
    
    # Create virtual environment
    if not os.path.exists('backend/venv'):
        print("Creating virtual environment...")
        if not run_command('python -m venv venv', cwd='backend'):
            return False
    
    # Determine activation script based on OS
    if platform.system() == 'Windows':
        activate_script = 'venv\\Scripts\\activate'
        pip_command = 'venv\\Scripts\\pip'
        python_command = 'venv\\Scripts\\python'
    else:
        activate_script = 'venv/bin/activate'
        pip_command = 'venv/bin/pip'
        python_command = 'venv/bin/python'
    
    # Install dependencies
    print("Installing Python dependencies...")
    if not run_command(f'{pip_command} install --upgrade pip', cwd='backend'):
        return False
    
    if not run_command(f'{pip_command} install -r requirements.txt', cwd='backend'):
        return False
    
    # Initialize database
    print("Initializing database...")
    if not run_command(f'{python_command} -c "from app import create_app, db; app = create_app(); app.app_context().push(); db.create_all(); print(\'Database initialized\')"', cwd='backend'):
        return False
    
    print("✓ Backend setup completed")
    return True

def setup_frontend():
    """Set up the React frontend"""
    print("\n🔧 Setting up React frontend...")
    
    # Install dependencies
    print("Installing Node.js dependencies...")
    if not run_command('npm install', cwd='frontend'):
        return False
    
    print("✓ Frontend setup completed")
    return True

def create_env_files():
    """Create environment configuration files"""
    print("\n🔧 Creating environment files...")
    
    # Backend .env file
    backend_env = """# Flask Configuration
FLASK_ENV=development
SECRET_KEY=your-secret-key-change-in-production
JWT_SECRET_KEY=jwt-secret-string-change-in-production

# Database Configuration
DATABASE_URL=sqlite:///database.db

# Email Configuration (Optional)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_DEFAULT_SENDER=<EMAIL>

# Company Information
COMPANY_NAME=Your Company Name
DEFAULT_CURRENCY=USD
DEFAULT_LANGUAGE=en
"""
    
    with open('backend/.env', 'w') as f:
        f.write(backend_env)
    
    print("✓ Environment files created")
    return True

def create_startup_scripts():
    """Create startup scripts for easy development"""
    print("\n🔧 Creating startup scripts...")
    
    # Windows batch file
    windows_script = """@echo off
echo Starting Business Management System...
echo.

echo Starting Backend Server...
cd backend
start "Backend" cmd /k "venv\\Scripts\\activate && python app.py"
cd ..

echo Starting Frontend Server...
cd frontend
start "Frontend" cmd /k "npm run dev"
cd ..

echo.
echo Both servers are starting...
echo Backend: http://localhost:5000
echo Frontend: http://localhost:3000
echo.
pause
"""
    
    with open('start.bat', 'w') as f:
        f.write(windows_script)
    
    # Unix shell script
    unix_script = """#!/bin/bash
echo "Starting Business Management System..."
echo

echo "Starting Backend Server..."
cd backend
source venv/bin/activate
python app.py &
BACKEND_PID=$!
cd ..

echo "Starting Frontend Server..."
cd frontend
npm run dev &
FRONTEND_PID=$!
cd ..

echo
echo "Both servers are starting..."
echo "Backend: http://localhost:5000"
echo "Frontend: http://localhost:3000"
echo
echo "Press Ctrl+C to stop both servers"

# Function to cleanup background processes
cleanup() {
    echo "Stopping servers..."
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    exit
}

# Set trap to cleanup on script exit
trap cleanup INT TERM

# Wait for background processes
wait
"""
    
    with open('start.sh', 'w') as f:
        f.write(unix_script)
    
    # Make shell script executable
    if platform.system() != 'Windows':
        os.chmod('start.sh', 0o755)
    
    print("✓ Startup scripts created")
    return True

def main():
    """Main setup function"""
    print("🚀 Business Management System Setup")
    print("=" * 50)
    
    # Check prerequisites
    if not check_python_version():
        return False
    
    if not check_node_version():
        return False
    
    # Setup components
    if not setup_backend():
        return False
    
    if not setup_frontend():
        return False
    
    if not create_env_files():
        return False
    
    if not create_startup_scripts():
        return False
    
    print("\n🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Review and update the .env file in the backend directory")
    print("2. Run the application:")
    if platform.system() == 'Windows':
        print("   - Windows: Double-click start.bat")
    else:
        print("   - Linux/Mac: ./start.sh")
    print("3. Open http://localhost:3000 in your browser")
    print("4. Login with: admin / admin123")
    print("\nFor manual startup:")
    print("Backend: cd backend && source venv/bin/activate && python app.py")
    print("Frontend: cd frontend && npm run dev")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
