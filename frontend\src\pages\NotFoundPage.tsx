import React from 'react';
import { Box, Typography, Button, Container } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { Home as HomeIcon } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

const NotFoundPage: React.FC = () => {
  const { i18n } = useTranslation();
  const navigate = useNavigate();

  return (
    <Container maxWidth="md">
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        minHeight="100vh"
        textAlign="center"
      >
        <Typography variant="h1" color="primary" fontWeight="bold" sx={{ fontSize: '8rem' }}>
          404
        </Typography>
        <Typography variant="h4" gutterBottom>
          {i18n.language === 'ar' ? 'الصفحة غير موجودة' : 'Page Not Found'}
        </Typography>
        <Typography variant="body1" color="textSecondary" paragraph>
          {i18n.language === 'ar' 
            ? 'عذراً، الصفحة التي تبحث عنها غير موجودة.'
            : 'Sorry, the page you are looking for does not exist.'
          }
        </Typography>
        <Button
          variant="contained"
          startIcon={<HomeIcon />}
          onClick={() => navigate('/dashboard')}
          size="large"
        >
          {i18n.language === 'ar' ? 'العودة للرئيسية' : 'Go to Dashboard'}
        </Button>
      </Box>
    </Container>
  );
};

export default NotFoundPage;
