from app import db
from datetime import datetime

class Inventory(db.Model):
    __tablename__ = 'inventory'
    
    id = db.Column(db.Integer, primary_key=True)
    product_id = db.Column(db.Integer, db.<PERSON>ey('products.id'), nullable=False)
    
    # Stock quantities
    quantity = db.Column(db.Integer, default=0)
    reserved_quantity = db.Column(db.Integer, default=0)  # Reserved for pending orders
    available_quantity = db.Column(db.Integer, default=0)  # quantity - reserved_quantity
    
    # Location
    warehouse_id = db.Column(db.Integer, db.ForeignKey('warehouses.id'))
    location = db.Column(db.String(50))  # Shelf, bin, etc.
    
    # Cost tracking
    average_cost = db.Column(db.Numeric(15, 2), default=0)
    last_cost = db.Column(db.Numeric(15, 2), default=0)
    
    # Branch association
    branch_id = db.Column(db.Integer, db.<PERSON><PERSON>ey('branches.id'))
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_movement_date = db.Column(db.DateTime)
    
    # Relationships
    branch = db.relationship('Branch', backref='inventory_items')
    warehouse = db.relationship('Warehouse', backref='inventory_items')
    movements = db.relationship('InventoryMovement', backref='inventory', lazy='dynamic')
    
    def update_available_quantity(self):
        """Update available quantity based on quantity and reserved"""
        self.available_quantity = self.quantity - self.reserved_quantity
        db.session.commit()
    
    def add_stock(self, quantity, cost=None, reference=None, notes=None):
        """Add stock to inventory"""
        if quantity <= 0:
            return False
        
        # Update quantities
        old_quantity = self.quantity
        self.quantity += quantity
        self.update_available_quantity()
        
        # Update average cost if cost is provided
        if cost:
            total_value = (old_quantity * float(self.average_cost)) + (quantity * cost)
            self.average_cost = total_value / self.quantity if self.quantity > 0 else 0
            self.last_cost = cost
        
        self.last_movement_date = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        
        # Create movement record
        movement = InventoryMovement(
            inventory_id=self.id,
            movement_type='in',
            quantity=quantity,
            cost_per_unit=cost,
            reference=reference,
            notes=notes,
            branch_id=self.branch_id
        )
        db.session.add(movement)
        db.session.commit()
        
        return True
    
    def remove_stock(self, quantity, reference=None, notes=None):
        """Remove stock from inventory"""
        if quantity <= 0 or quantity > self.available_quantity:
            return False
        
        # Update quantities
        self.quantity -= quantity
        self.update_available_quantity()
        self.last_movement_date = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        
        # Create movement record
        movement = InventoryMovement(
            inventory_id=self.id,
            movement_type='out',
            quantity=quantity,
            cost_per_unit=self.average_cost,
            reference=reference,
            notes=notes,
            branch_id=self.branch_id
        )
        db.session.add(movement)
        db.session.commit()
        
        return True
    
    def reserve_stock(self, quantity):
        """Reserve stock for pending orders"""
        if quantity <= 0 or quantity > self.available_quantity:
            return False
        
        self.reserved_quantity += quantity
        self.update_available_quantity()
        return True
    
    def release_reserved_stock(self, quantity):
        """Release reserved stock"""
        if quantity <= 0 or quantity > self.reserved_quantity:
            return False
        
        self.reserved_quantity -= quantity
        self.update_available_quantity()
        return True
    
    def to_dict(self):
        """Convert inventory object to dictionary"""
        return {
            'id': self.id,
            'product_id': self.product_id,
            'quantity': self.quantity,
            'reserved_quantity': self.reserved_quantity,
            'available_quantity': self.available_quantity,
            'warehouse_id': self.warehouse_id,
            'location': self.location,
            'average_cost': float(self.average_cost) if self.average_cost else 0,
            'last_cost': float(self.last_cost) if self.last_cost else 0,
            'branch_id': self.branch_id,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'last_movement_date': self.last_movement_date.isoformat() if self.last_movement_date else None
        }
    
    def __repr__(self):
        return f'<Inventory Product {self.product_id}: {self.quantity}>'

class InventoryMovement(db.Model):
    __tablename__ = 'inventory_movements'
    
    id = db.Column(db.Integer, primary_key=True)
    inventory_id = db.Column(db.Integer, db.ForeignKey('inventory.id'), nullable=False)
    
    # Movement details
    movement_type = db.Column(db.String(20), nullable=False)  # in, out, adjustment, transfer
    quantity = db.Column(db.Integer, nullable=False)
    cost_per_unit = db.Column(db.Numeric(15, 2))
    
    # Reference information
    reference = db.Column(db.String(100))  # Purchase order, sale order, etc.
    reference_type = db.Column(db.String(50))  # purchase, sale, adjustment, transfer
    reference_id = db.Column(db.Integer)  # ID of the reference document
    
    # Additional information
    notes = db.Column(db.Text)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # Branch association
    branch_id = db.Column(db.Integer, db.ForeignKey('branches.id'))
    
    # Timestamp
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    branch = db.relationship('Branch', backref='inventory_movements')
    user = db.relationship('User', backref='inventory_movements')
    
    def to_dict(self):
        """Convert movement object to dictionary"""
        return {
            'id': self.id,
            'inventory_id': self.inventory_id,
            'movement_type': self.movement_type,
            'quantity': self.quantity,
            'cost_per_unit': float(self.cost_per_unit) if self.cost_per_unit else 0,
            'reference': self.reference,
            'reference_type': self.reference_type,
            'reference_id': self.reference_id,
            'notes': self.notes,
            'user_id': self.user_id,
            'branch_id': self.branch_id,
            'created_at': self.created_at.isoformat()
        }
    
    def __repr__(self):
        return f'<InventoryMovement {self.movement_type}: {self.quantity}>'

class Warehouse(db.Model):
    __tablename__ = 'warehouses'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    code = db.Column(db.String(20), unique=True, nullable=False)
    
    # Location
    address = db.Column(db.Text)
    city = db.Column(db.String(50))
    state = db.Column(db.String(50))
    postal_code = db.Column(db.String(20))
    country = db.Column(db.String(50))
    
    # Contact
    manager_name = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    
    # Status
    is_active = db.Column(db.Boolean, default=True)
    is_main = db.Column(db.Boolean, default=False)
    
    # Branch association
    branch_id = db.Column(db.Integer, db.ForeignKey('branches.id'))
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    branch = db.relationship('Branch', backref='warehouses')
    
    def to_dict(self):
        """Convert warehouse object to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'code': self.code,
            'address': self.address,
            'city': self.city,
            'state': self.state,
            'postal_code': self.postal_code,
            'country': self.country,
            'manager_name': self.manager_name,
            'phone': self.phone,
            'email': self.email,
            'is_active': self.is_active,
            'is_main': self.is_main,
            'branch_id': self.branch_id,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def __repr__(self):
        return f'<Warehouse {self.name}>'
