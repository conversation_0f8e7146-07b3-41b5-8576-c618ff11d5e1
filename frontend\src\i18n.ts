import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// Translation resources
const resources = {
  en: {
    translation: {
      // Navigation
      dashboard: 'Dashboard',
      purchases: 'Purchases',
      suppliers: 'Suppliers',
      customers: 'Customers',
      sales: 'Sales',
      inventory: 'Inventory',
      accounting: 'Accounting',
      reports: 'Reports',
      invoices: 'Invoices',
      employees: 'Employees',
      settings: 'Settings',
      
      // Common
      add: 'Add',
      edit: 'Edit',
      delete: 'Delete',
      save: 'Save',
      cancel: 'Cancel',
      search: 'Search',
      filter: 'Filter',
      export: 'Export',
      import: 'Import',
      print: 'Print',
      loading: 'Loading...',
      noData: 'No data available',
      
      // Authentication
      login: 'Login',
      logout: 'Logout',
      username: 'Userna<PERSON>',
      password: 'Password',
      email: 'Email',
      forgotPassword: 'Forgot Password?',
      
      // Dashboard
      totalSales: 'Total Sales',
      totalPurchases: 'Total Purchases',
      totalCustomers: 'Total Customers',
      totalSuppliers: 'Total Suppliers',
      pendingInvoices: 'Pending Invoices',
      lowStock: 'Low Stock Items',
      recentActivities: 'Recent Activities',
      
      // Forms
      name: 'Name',
      code: 'Code',
      description: 'Description',
      phone: 'Phone',
      address: 'Address',
      city: 'City',
      country: 'Country',
      status: 'Status',
      active: 'Active',
      inactive: 'Inactive',
      
      // Messages
      saveSuccess: 'Saved successfully',
      deleteSuccess: 'Deleted successfully',
      error: 'An error occurred',
      confirmDelete: 'Are you sure you want to delete this item?',
    }
  },
  ar: {
    translation: {
      // Navigation
      dashboard: 'لوحة التحكم',
      purchases: 'المشتريات',
      suppliers: 'الموردين',
      customers: 'العملاء',
      sales: 'المبيعات',
      inventory: 'المخزون',
      accounting: 'المحاسبة',
      reports: 'التقارير',
      invoices: 'الفواتير',
      employees: 'الموظفين',
      settings: 'الإعدادات',
      
      // Common
      add: 'إضافة',
      edit: 'تعديل',
      delete: 'حذف',
      save: 'حفظ',
      cancel: 'إلغاء',
      search: 'بحث',
      filter: 'تصفية',
      export: 'تصدير',
      import: 'استيراد',
      print: 'طباعة',
      loading: 'جاري التحميل...',
      noData: 'لا توجد بيانات',
      
      // Authentication
      login: 'تسجيل الدخول',
      logout: 'تسجيل الخروج',
      username: 'اسم المستخدم',
      password: 'كلمة المرور',
      email: 'البريد الإلكتروني',
      forgotPassword: 'نسيت كلمة المرور؟',
      
      // Dashboard
      totalSales: 'إجمالي المبيعات',
      totalPurchases: 'إجمالي المشتريات',
      totalCustomers: 'إجمالي العملاء',
      totalSuppliers: 'إجمالي الموردين',
      pendingInvoices: 'الفواتير المعلقة',
      lowStock: 'المنتجات منخفضة المخزون',
      recentActivities: 'الأنشطة الحديثة',
      
      // Forms
      name: 'الاسم',
      code: 'الكود',
      description: 'الوصف',
      phone: 'الهاتف',
      address: 'العنوان',
      city: 'المدينة',
      country: 'البلد',
      status: 'الحالة',
      active: 'نشط',
      inactive: 'غير نشط',
      
      // Messages
      saveSuccess: 'تم الحفظ بنجاح',
      deleteSuccess: 'تم الحذف بنجاح',
      error: 'حدث خطأ',
      confirmDelete: 'هل أنت متأكد من حذف هذا العنصر؟',
    }
  }
};

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: 'en', // default language
    fallbackLng: 'en',
    
    interpolation: {
      escapeValue: false, // React already escapes values
    },
    
    react: {
      useSuspense: false,
    },
  });

export default i18n;
