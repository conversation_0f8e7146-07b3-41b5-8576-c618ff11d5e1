from app import db
from datetime import datetime

class Sale(db.Model):
    __tablename__ = 'sales'
    
    id = db.Column(db.Integer, primary_key=True)
    sale_number = db.Column(db.String(50), unique=True, nullable=False)
    
    # Customer information
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'))
    customer_name = db.Column(db.String(100))  # For walk-in customers
    
    # Sale details
    sale_date = db.Column(db.DateTime, default=datetime.utcnow)
    due_date = db.Column(db.DateTime)
    
    # Financial information
    subtotal = db.Column(db.Numeric(15, 2), default=0)
    tax_amount = db.Column(db.Numeric(15, 2), default=0)
    discount_amount = db.Column(db.Numeric(15, 2), default=0)
    total_amount = db.Column(db.Numeric(15, 2), default=0)
    paid_amount = db.Column(db.Numeric(15, 2), default=0)
    balance_due = db.Column(db.Numeric(15, 2), default=0)
    
    # Payment information
    payment_method = db.Column(db.String(50))  # cash, card, bank_transfer, etc.
    payment_status = db.Column(db.String(20), default='pending')  # pending, partial, paid
    
    # Status and type
    status = db.Column(db.String(20), default='draft')  # draft, confirmed, delivered, cancelled
    sale_type = db.Column(db.String(20), default='regular')  # regular, return, exchange
    
    # Additional information
    notes = db.Column(db.Text)
    internal_notes = db.Column(db.Text)
    
    # Staff information
    salesperson_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # Branch association
    branch_id = db.Column(db.Integer, db.ForeignKey('branches.id'))
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    branch = db.relationship('Branch', backref='sales')
    salesperson = db.relationship('User', backref='sales')
    items = db.relationship('SaleItem', backref='sale', lazy='dynamic', cascade='all, delete-orphan')
    
    def calculate_totals(self):
        """Calculate sale totals from items"""
        subtotal = sum(item.total_amount for item in self.items)
        self.subtotal = subtotal
        
        # Calculate tax (simplified - could be more complex)
        self.tax_amount = subtotal * 0.1  # 10% tax rate
        
        # Apply discount
        self.total_amount = subtotal + self.tax_amount - self.discount_amount
        self.balance_due = self.total_amount - self.paid_amount
        
        db.session.commit()
    
    def add_payment(self, amount, method='cash', notes=None):
        """Add payment to sale"""
        if amount <= 0:
            return False
        
        self.paid_amount += amount
        self.balance_due = self.total_amount - self.paid_amount
        
        if self.balance_due <= 0:
            self.payment_status = 'paid'
        elif self.paid_amount > 0:
            self.payment_status = 'partial'
        
        # Create payment record (would need Payment model)
        # For now, just update the sale
        
        db.session.commit()
        return True
    
    def to_dict(self):
        """Convert sale object to dictionary"""
        return {
            'id': self.id,
            'sale_number': self.sale_number,
            'customer_id': self.customer_id,
            'customer_name': self.customer_name,
            'sale_date': self.sale_date.isoformat(),
            'due_date': self.due_date.isoformat() if self.due_date else None,
            'subtotal': float(self.subtotal) if self.subtotal else 0,
            'tax_amount': float(self.tax_amount) if self.tax_amount else 0,
            'discount_amount': float(self.discount_amount) if self.discount_amount else 0,
            'total_amount': float(self.total_amount) if self.total_amount else 0,
            'paid_amount': float(self.paid_amount) if self.paid_amount else 0,
            'balance_due': float(self.balance_due) if self.balance_due else 0,
            'payment_method': self.payment_method,
            'payment_status': self.payment_status,
            'status': self.status,
            'sale_type': self.sale_type,
            'notes': self.notes,
            'internal_notes': self.internal_notes,
            'salesperson_id': self.salesperson_id,
            'branch_id': self.branch_id,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'items': [item.to_dict() for item in self.items]
        }
    
    def __repr__(self):
        return f'<Sale {self.sale_number}>'

class SaleItem(db.Model):
    __tablename__ = 'sale_items'
    
    id = db.Column(db.Integer, primary_key=True)
    sale_id = db.Column(db.Integer, db.ForeignKey('sales.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False)
    
    # Item details
    product_name = db.Column(db.String(100), nullable=False)  # Snapshot of product name
    product_code = db.Column(db.String(50))  # Snapshot of product code
    
    # Quantities and pricing
    quantity = db.Column(db.Integer, nullable=False, default=1)
    unit_price = db.Column(db.Numeric(15, 2), nullable=False)
    discount_percentage = db.Column(db.Numeric(5, 2), default=0)
    discount_amount = db.Column(db.Numeric(15, 2), default=0)
    tax_rate = db.Column(db.Numeric(5, 2), default=0)
    tax_amount = db.Column(db.Numeric(15, 2), default=0)
    total_amount = db.Column(db.Numeric(15, 2), nullable=False)
    
    # Additional information
    notes = db.Column(db.Text)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    product = db.relationship('Product', backref='sale_items')
    
    def calculate_total(self):
        """Calculate item total"""
        subtotal = self.quantity * self.unit_price
        discount = subtotal * (self.discount_percentage / 100) if self.discount_percentage else self.discount_amount
        taxable_amount = subtotal - discount
        tax = taxable_amount * (self.tax_rate / 100)
        self.total_amount = taxable_amount + tax
        return self.total_amount
    
    def to_dict(self):
        """Convert sale item object to dictionary"""
        return {
            'id': self.id,
            'sale_id': self.sale_id,
            'product_id': self.product_id,
            'product_name': self.product_name,
            'product_code': self.product_code,
            'quantity': self.quantity,
            'unit_price': float(self.unit_price) if self.unit_price else 0,
            'discount_percentage': float(self.discount_percentage) if self.discount_percentage else 0,
            'discount_amount': float(self.discount_amount) if self.discount_amount else 0,
            'tax_rate': float(self.tax_rate) if self.tax_rate else 0,
            'tax_amount': float(self.tax_amount) if self.tax_amount else 0,
            'total_amount': float(self.total_amount) if self.total_amount else 0,
            'notes': self.notes,
            'created_at': self.created_at.isoformat()
        }
    
    def __repr__(self):
        return f'<SaleItem {self.product_name}: {self.quantity}>'
