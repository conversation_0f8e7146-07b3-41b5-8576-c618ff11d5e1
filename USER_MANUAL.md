# User Manual - دليل المستخدم
## نظام إدارة الأعمال المتكامل - Integrated Business Management System

### Table of Contents - جدول المحتويات

1. [Getting Started - البدء](#getting-started)
2. [Dashboard - لوحة التحكم](#dashboard)
3. [Purchase Management - إدارة المشتريات](#purchase-management)
4. [Supplier Management - إدارة الموردين](#supplier-management)
5. [Customer Management - إدارة العملاء](#customer-management)
6. [Sales Management - إدارة المبيعات](#sales-management)
7. [Inventory Management - إدارة المخزون](#inventory-management)
8. [Accounting - المحاسبة](#accounting)
9. [Financial Reports - التقارير المالية](#financial-reports)
10. [Invoice Management - إدارة الفواتير](#invoice-management)
11. [Employee Management - إدارة الموظفين](#employee-management)
12. [System Settings - إعدادات النظام](#system-settings)

---

## Getting Started - البدء

### System Login - تسجيل الدخول للنظام

1. **Access the System - الوصول للنظام**
   - Open your web browser
   - Navigate to: `http://localhost:3000`
   - You will see the login screen

2. **Login Credentials - بيانات تسجيل الدخول**
   - **Username**: admin
   - **Password**: admin123
   - Click "Login" button

3. **Language Selection - اختيار اللغة**
   - Click the language icon (🌐) in the top-right corner
   - Choose between English and Arabic (العربية)
   - The interface will automatically adjust direction (RTL for Arabic)

### First Time Setup - الإعداد الأولي

After logging in for the first time:

1. **Change Default Password - تغيير كلمة المرور الافتراضية**
   - Click on your profile avatar in the top-right
   - Select "Profile" from the dropdown
   - Click "Change Password"
   - Enter current password: `admin123`
   - Enter your new secure password

2. **Company Information - معلومات الشركة**
   - Go to Settings → System Settings
   - Update company name, logo, and contact information
   - Set default currency and tax rates

---

## Dashboard - لوحة التحكم

The dashboard provides a comprehensive overview of your business operations.

### Key Performance Indicators - مؤشرات الأداء الرئيسية

**Sales Metrics - مقاييس المبيعات:**
- Today's Sales - مبيعات اليوم
- Weekly Sales - المبيعات الأسبوعية  
- Monthly Sales - المبيعات الشهرية
- Annual Sales - المبيعات السنوية

**Customer Metrics - مقاييس العملاء:**
- Total Customers - إجمالي العملاء
- New Customers This Month - العملاء الجدد هذا الشهر
- VIP Customers - العملاء المميزون

**Inventory Alerts - تنبيهات المخزون:**
- Low Stock Items - المنتجات منخفضة المخزون
- Out of Stock Items - المنتجات غير المتوفرة
- Reorder Alerts - تنبيهات إعادة الطلب

**Financial Overview - النظرة المالية:**
- Pending Invoices - الفواتير المعلقة
- Overdue Payments - المدفوعات المتأخرة
- Monthly Profit - الربح الشهري

### Charts and Analytics - الرسوم البيانية والتحليلات

**Sales Trend Chart - رسم بياني لاتجاه المبيعات:**
- Shows 12-month sales history
- Interactive chart with hover details
- Export functionality for reports

**Top Products Chart - رسم بياني للمنتجات الأكثر مبيعاً:**
- Displays best-selling products
- Quantity and revenue breakdown
- Helps identify popular items

### Recent Activities - الأنشطة الحديثة

- Latest sales transactions
- Recent purchase orders
- New customer registrations
- Invoice payments received

---

## Purchase Management - إدارة المشتريات

### Creating Purchase Orders - إنشاء أوامر الشراء

1. **Navigate to Purchases - الانتقال للمشتريات**
   - Click "Purchases" in the sidebar
   - Click "Add Purchase Order" button

2. **Purchase Order Details - تفاصيل أمر الشراء**
   - **Supplier**: Select from dropdown or add new
   - **Purchase Date**: Default is today
   - **Expected Delivery**: Set expected arrival date
   - **Payment Terms**: Net 30, Net 60, etc.
   - **Reference Number**: Internal reference

3. **Adding Items - إضافة المنتجات**
   - Click "Add Item" button
   - Select product from catalog
   - Enter quantity and unit cost
   - System calculates totals automatically

4. **Review and Submit - المراجعة والإرسال**
   - Review all details
   - Add notes if needed
   - Click "Save as Draft" or "Submit Order"

### Purchase Order Status - حالة أوامر الشراء

- **Draft - مسودة**: Order being prepared
- **Ordered - مُرسل**: Sent to supplier
- **Received - مُستلم**: Items received
- **Cancelled - ملغي**: Order cancelled

### Receiving Goods - استلام البضائع

1. **Open Purchase Order - فتح أمر الشراء**
   - Find the order in "Ordered" status
   - Click "Receive Items"

2. **Record Received Quantities - تسجيل الكميات المستلمة**
   - Enter actual quantities received
   - Note any discrepancies
   - Update inventory automatically

3. **Quality Check - فحص الجودة**
   - Mark items as accepted/rejected
   - Add quality notes
   - Generate receiving report

---

## Supplier Management - إدارة الموردين

### Adding New Suppliers - إضافة موردين جدد

1. **Supplier Information - معلومات المورد**
   - **Code**: Unique supplier identifier
   - **Name**: Company name (English)
   - **Arabic Name**: Company name in Arabic
   - **Contact Person**: Primary contact

2. **Contact Details - تفاصيل الاتصال**
   - Email address
   - Phone and mobile numbers
   - Fax number
   - Website URL

3. **Address Information - معلومات العنوان**
   - Street address
   - City and state
   - Postal code
   - Country

4. **Business Details - التفاصيل التجارية**
   - Tax ID number
   - Registration number
   - Business type
   - Industry category

5. **Financial Settings - الإعدادات المالية**
   - Credit limit
   - Payment terms
   - Currency preference
   - Banking information

### Supplier Performance - أداء الموردين

**Rating System - نظام التقييم:**
- 1-10 scale rating
- Based on delivery time, quality, service
- Automatic calculation from order history

**Performance Metrics - مقاييس الأداء:**
- Total purchase amount
- Number of orders
- On-time delivery rate
- Quality score

### Supplier Categories - فئات الموردين

- **Preferred Suppliers - الموردون المفضلون**: Best performers
- **Regular Suppliers - الموردون العاديون**: Standard suppliers
- **Blocked Suppliers - الموردون المحظورون**: Suspended suppliers

---

## Customer Management - إدارة العملاء

### Customer Types - أنواع العملاء

**Individual Customers - العملاء الأفراد:**
- Personal information
- Contact details
- Purchase history

**Corporate Customers - العملاء المؤسسيون:**
- Company information
- Tax registration
- Credit terms
- Multiple contacts

### Customer Information - معلومات العملاء

1. **Basic Information - المعلومات الأساسية**
   - Customer code
   - Name (English and Arabic)
   - Customer type
   - Contact person

2. **Contact Details - تفاصيل الاتصال**
   - Email and phone
   - Address information
   - Preferred language

3. **Business Information - المعلومات التجارية**
   - Tax ID
   - Credit limit
   - Payment terms
   - Discount percentage

4. **Classification - التصنيف**
   - Customer category (VIP, Regular, New)
   - Source (how they found you)
   - Assigned salesperson

### Customer Analytics - تحليلات العملاء

**Purchase Behavior - سلوك الشراء:**
- Total sales amount
- Purchase frequency
- Average order value
- Last purchase date

**Customer Lifetime Value - القيمة الإجمالية للعميل:**
- Historical purchase total
- Projected future value
- Profitability analysis

**Payment History - تاريخ المدفوعات:**
- Payment punctuality
- Outstanding balance
- Credit utilization

---

## Sales Management - إدارة المبيعات

### Point of Sale (POS) - نقطة البيع

1. **Quick Sale - البيع السريع**
   - Scan barcode or search product
   - Enter quantity
   - Apply discounts if needed
   - Process payment

2. **Customer Selection - اختيار العميل**
   - Search existing customer
   - Add walk-in customer
   - Apply customer discounts

3. **Payment Processing - معالجة الدفع**
   - Cash payment
   - Credit/debit card
   - Bank transfer
   - Split payments

### Sales Orders - أوامر المبيعات

**Order Creation - إنشاء الأوامر:**
1. Select customer
2. Add products and quantities
3. Set delivery date
4. Apply terms and conditions
5. Generate order confirmation

**Order Fulfillment - تنفيذ الأوامر:**
1. Pick items from inventory
2. Pack for delivery
3. Generate delivery note
4. Update order status
5. Create invoice

### Sales Analytics - تحليلات المبيعات

**Performance Metrics - مقاييس الأداء:**
- Daily/weekly/monthly sales
- Sales by product category
- Sales by salesperson
- Profit margins

**Trend Analysis - تحليل الاتجاهات:**
- Seasonal patterns
- Growth trends
- Product performance
- Customer segments

---

## Inventory Management - إدارة المخزون

### Product Catalog - كتالوج المنتجات

1. **Product Information - معلومات المنتج**
   - Product code and barcode
   - Name (English and Arabic)
   - Description
   - Category and brand

2. **Pricing - التسعير**
   - Cost price
   - Selling price
   - Wholesale price
   - Tax rates

3. **Inventory Settings - إعدادات المخزون**
   - Minimum stock level
   - Maximum stock level
   - Reorder point
   - Track inventory (yes/no)

4. **Physical Properties - الخصائص الفيزيائية**
   - Weight and dimensions
   - Color and size
   - Storage requirements

### Stock Management - إدارة المخزون

**Stock Movements - حركات المخزون:**
- Stock in (purchases, returns)
- Stock out (sales, adjustments)
- Stock transfers between locations
- Stock adjustments

**Inventory Tracking - تتبع المخزون:**
- Real-time stock levels
- Reserved quantities
- Available quantities
- Location tracking

**Stock Alerts - تنبيهات المخزون:**
- Low stock warnings
- Out of stock alerts
- Reorder notifications
- Expiry date alerts

### Warehouse Management - إدارة المستودعات

**Multiple Locations - مواقع متعددة:**
- Main warehouse
- Branch locations
- Storage areas
- Bin locations

**Stock Transfers - نقل المخزون:**
- Between warehouses
- Between locations
- Transfer documentation
- Approval workflow

---

## Accounting - المحاسبة

### Chart of Accounts - دليل الحسابات

**Account Types - أنواع الحسابات:**
- Assets - الأصول
- Liabilities - الخصوم
- Equity - حقوق الملكية
- Revenue - الإيرادات
- Expenses - المصروفات

**Account Structure - هيكل الحسابات:**
- Account code
- Account name (English/Arabic)
- Account type
- Parent account
- Active status

### Journal Entries - القيود المحاسبية

1. **Manual Entries - القيود اليدوية**
   - Date and reference
   - Description
   - Debit and credit accounts
   - Amounts
   - Supporting documents

2. **Automatic Entries - القيود التلقائية**
   - Sales transactions
   - Purchase transactions
   - Payment receipts
   - Inventory adjustments

### General Ledger - الأستاذ العام

**Account Balances - أرصدة الحسابات:**
- Opening balance
- Debit movements
- Credit movements
- Closing balance

**Trial Balance - ميزان المراجعة:**
- All account balances
- Debit and credit totals
- Balance verification
- Export to Excel

---

## Financial Reports - التقارير المالية

### Profit & Loss Statement - قائمة الأرباح والخسائر

**Revenue Section - قسم الإيرادات:**
- Sales revenue
- Other income
- Total revenue

**Expense Section - قسم المصروفات:**
- Cost of goods sold
- Operating expenses
- Other expenses
- Total expenses

**Profit Calculation - حساب الربح:**
- Gross profit
- Operating profit
- Net profit
- Profit margins

### Balance Sheet - الميزانية العمومية

**Assets - الأصول:**
- Current assets
- Fixed assets
- Total assets

**Liabilities - الخصوم:**
- Current liabilities
- Long-term liabilities
- Total liabilities

**Equity - حقوق الملكية:**
- Capital
- Retained earnings
- Total equity

### Cash Flow Statement - قائمة التدفقات النقدية

**Operating Activities - الأنشطة التشغيلية:**
- Cash from sales
- Cash to suppliers
- Operating cash flow

**Investing Activities - الأنشطة الاستثمارية:**
- Asset purchases
- Asset sales
- Investment cash flow

**Financing Activities - الأنشطة التمويلية:**
- Loans received
- Loan payments
- Financing cash flow

### Tax Reports - التقارير الضريبية

**VAT Reports - تقارير ضريبة القيمة المضافة:**
- Input VAT
- Output VAT
- VAT payable/refundable

**Income Tax - ضريبة الدخل:**
- Taxable income
- Tax calculations
- Tax payments

---

## Invoice Management - إدارة الفواتير

### Creating Invoices - إنشاء الفواتير

1. **Invoice Header - رأس الفاتورة**
   - Customer selection
   - Invoice date
   - Due date
   - Payment terms

2. **Invoice Items - بنود الفاتورة**
   - Product/service description
   - Quantity and unit price
   - Discounts and taxes
   - Line totals

3. **Invoice Totals - إجماليات الفاتورة**
   - Subtotal
   - Tax amount
   - Discount amount
   - Total amount

### Invoice Status Management - إدارة حالة الفواتير

**Status Types - أنواع الحالات:**
- **Draft - مسودة**: Being prepared
- **Sent - مُرسلة**: Sent to customer
- **Pending - معلقة**: Awaiting payment
- **Paid - مدفوعة**: Fully paid
- **Overdue - متأخرة**: Past due date
- **Cancelled - ملغية**: Cancelled

### Payment Processing - معالجة المدفوعات

1. **Recording Payments - تسجيل المدفوعات**
   - Payment amount
   - Payment method
   - Payment date
   - Reference number

2. **Partial Payments - المدفوعات الجزئية**
   - Multiple payment entries
   - Outstanding balance tracking
   - Payment history

3. **Payment Matching - مطابقة المدفوعات**
   - Bank statement reconciliation
   - Automatic matching
   - Manual adjustments

### Invoice Templates - قوالب الفواتير

**Template Customization - تخصيص القوالب:**
- Company logo and information
- Invoice layout and design
- Terms and conditions
- Multiple language support

**PDF Generation - إنتاج ملفات PDF:**
- Professional invoice format
- Email delivery
- Print functionality
- Archive storage

---

## Employee Management - إدارة الموظفين

### Employee Records - سجلات الموظفين

1. **Personal Information - المعلومات الشخصية**
   - Employee number
   - Full name (English/Arabic)
   - Contact information
   - Address details

2. **Employment Details - تفاصيل التوظيف**
   - Position and department
   - Hire date
   - Employment type
   - Reporting manager

3. **Compensation - التعويضات**
   - Base salary
   - Currency
   - Pay frequency
   - Benefits

### Attendance Management - إدارة الحضور

**Time Tracking - تتبع الوقت:**
- Clock in/out times
- Break periods
- Overtime hours
- Absence tracking

**Leave Management - إدارة الإجازات:**
- Leave types (annual, sick, etc.)
- Leave balance
- Leave requests
- Approval workflow

### Payroll Processing - معالجة الرواتب

**Salary Calculation - حساب الراتب:**
- Basic salary
- Allowances
- Deductions
- Net salary

**Payroll Reports - تقارير الرواتب:**
- Payslip generation
- Payroll summary
- Tax calculations
- Bank transfer files

---

## System Settings - إعدادات النظام

### Company Settings - إعدادات الشركة

**Basic Information - المعلومات الأساسية:**
- Company name
- Logo upload
- Contact information
- Tax registration

**Localization - التوطين:**
- Default language
- Currency settings
- Date format
- Number format

**Tax Configuration - تكوين الضرائب:**
- Tax rates
- Tax types
- Tax calculations
- Tax reports

### User Management - إدارة المستخدمين

**User Accounts - حسابات المستخدمين:**
- Username and password
- Personal information
- Contact details
- Account status

**Role-Based Access - الوصول القائم على الأدوار:**
- Admin: Full system access
- Manager: Department access
- User: Limited access
- Viewer: Read-only access

**Permissions - الصلاحيات:**
- Module access
- Feature permissions
- Data restrictions
- Branch limitations

### System Configuration - تكوين النظام

**General Settings - الإعدادات العامة:**
- System timezone
- Backup schedule
- Email settings
- Notification preferences

**Security Settings - إعدادات الأمان:**
- Password policies
- Session timeout
- Two-factor authentication
- Audit logging

**Integration Settings - إعدادات التكامل:**
- API configuration
- Third-party services
- Data import/export
- Webhook settings

### Backup and Restore - النسخ الاحتياطي والاستعادة

**Automatic Backups - النسخ الاحتياطي التلقائي:**
- Daily database backup
- File system backup
- Cloud storage sync
- Retention policies

**Manual Backup - النسخ الاحتياطي اليدوي:**
- On-demand backup
- Selective backup
- Export functionality
- Backup verification

**Data Restore - استعادة البيانات:**
- Point-in-time recovery
- Selective restore
- Data validation
- System rollback

---

## Tips and Best Practices - نصائح وأفضل الممارسات

### Data Entry Best Practices - أفضل ممارسات إدخال البيانات

1. **Consistency - الاتساق**
   - Use standard naming conventions
   - Maintain consistent data formats
   - Regular data validation

2. **Accuracy - الدقة**
   - Double-check important entries
   - Use dropdown lists when possible
   - Implement data validation rules

3. **Completeness - الاكتمال**
   - Fill all required fields
   - Add relevant notes and descriptions
   - Maintain up-to-date information

### Security Best Practices - أفضل ممارسات الأمان

1. **Password Security - أمان كلمة المرور**
   - Use strong, unique passwords
   - Change passwords regularly
   - Enable two-factor authentication

2. **Access Control - التحكم في الوصول**
   - Assign minimum required permissions
   - Regular access reviews
   - Disable inactive accounts

3. **Data Protection - حماية البيانات**
   - Regular backups
   - Secure data transmission
   - Monitor system access

### Performance Optimization - تحسين الأداء

1. **Regular Maintenance - الصيانة الدورية**
   - Database optimization
   - Clear temporary files
   - Update system regularly

2. **Efficient Workflows - سير العمل الفعال**
   - Use keyboard shortcuts
   - Batch operations
   - Automate repetitive tasks

3. **Resource Management - إدارة الموارد**
   - Monitor system resources
   - Optimize database queries
   - Regular performance reviews

---

## Troubleshooting - استكشاف الأخطاء وإصلاحها

### Common Issues - المشاكل الشائعة

**Login Problems - مشاكل تسجيل الدخول:**
- Verify username and password
- Check account status
- Clear browser cache

**Performance Issues - مشاكل الأداء:**
- Check internet connection
- Close unnecessary browser tabs
- Restart the application

**Data Issues - مشاكل البيانات:**
- Verify data entry
- Check validation rules
- Contact system administrator

### Getting Help - الحصول على المساعدة

**Documentation - التوثيق:**
- User manual (this document)
- Video tutorials
- FAQ section

**Support Channels - قنوات الدعم:**
- Help desk tickets
- Email support
- Phone support
- Live chat

**Training Resources - موارد التدريب:**
- Online training modules
- Webinar sessions
- User workshops
- Best practices guides

---

## Appendices - الملاحق

### Keyboard Shortcuts - اختصارات لوحة المفاتيح

**General Navigation - التنقل العام:**
- `Ctrl + S`: Save
- `Ctrl + N`: New record
- `Ctrl + F`: Search
- `Esc`: Cancel/Close

**Data Entry - إدخال البيانات:**
- `Tab`: Next field
- `Shift + Tab`: Previous field
- `Enter`: Confirm entry
- `F2`: Edit mode

### System Limits - حدود النظام

**Data Limits - حدود البيانات:**
- Maximum users: 100
- Maximum products: 10,000
- Maximum transactions per day: 1,000
- File upload size: 16MB

**Performance Limits - حدود الأداء:**
- Concurrent users: 50
- Database size: 10GB
- Backup retention: 30 days
- Session timeout: 2 hours

### Glossary - المسرد

**A-C:**
- **Account**: حساب
- **Balance**: رصيد
- **Customer**: عميل

**D-I:**
- **Dashboard**: لوحة التحكم
- **Invoice**: فاتورة
- **Inventory**: مخزون

**P-S:**
- **Purchase**: شراء
- **Sale**: بيع
- **Supplier**: مورد

**T-Z:**
- **Transaction**: معاملة
- **User**: مستخدم
- **Warehouse**: مستودع

---

*This manual is regularly updated. For the latest version, please check the system documentation section.*

*يتم تحديث هذا الدليل بانتظام. للحصول على أحدث إصدار، يرجى مراجعة قسم التوثيق في النظام.*
