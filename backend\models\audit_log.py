from app import db
from datetime import datetime

class AuditLog(db.Model):
    __tablename__ = 'audit_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>Key('users.id'), nullable=True)
    
    # Action details
    action = db.Column(db.String(50), nullable=False)  # login, logout, create, update, delete, etc.
    resource_type = db.Column(db.String(50))  # user, customer, supplier, product, etc.
    resource_id = db.Column(db.Integer)  # ID of the affected resource
    
    # Request details
    details = db.Column(db.Text)  # Detailed description of the action
    ip_address = db.Column(db.String(45))  # IPv4 or IPv6
    user_agent = db.Column(db.String(255))  # Browser/client information
    
    # Additional context
    old_values = db.Column(db.Text)  # JSON string of old values (for updates)
    new_values = db.Column(db.Text)  # JSON string of new values (for updates)
    
    # Status and result
    status = db.Column(db.String(20), default='success')  # success, failed, error
    error_message = db.Column(db.Text)  # Error details if status is failed/error
    
    # Branch context
    branch_id = db.Column(db.Integer, db.ForeignKey('branches.id'))
    
    # Timestamp
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    
    # Relationships
    branch = db.relationship('Branch', backref='audit_logs')
    
    @staticmethod
    def log_action(user_id, action, resource_type=None, resource_id=None, 
                   details=None, ip_address=None, user_agent=None,
                   old_values=None, new_values=None, status='success',
                   error_message=None, branch_id=None):
        """Helper method to create audit log entries"""
        audit_log = AuditLog(
            user_id=user_id,
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            details=details,
            ip_address=ip_address,
            user_agent=user_agent,
            old_values=old_values,
            new_values=new_values,
            status=status,
            error_message=error_message,
            branch_id=branch_id
        )
        db.session.add(audit_log)
        return audit_log
    
    def to_dict(self):
        """Convert audit log object to dictionary"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'action': self.action,
            'resource_type': self.resource_type,
            'resource_id': self.resource_id,
            'details': self.details,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'old_values': self.old_values,
            'new_values': self.new_values,
            'status': self.status,
            'error_message': self.error_message,
            'branch_id': self.branch_id,
            'created_at': self.created_at.isoformat(),
            'user': self.user.to_dict() if self.user else None
        }
    
    def __repr__(self):
        return f'<AuditLog {self.action} by User {self.user_id}>'
