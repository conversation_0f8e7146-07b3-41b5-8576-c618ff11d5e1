import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  IconButton,
  InputAdornment,
  FormControlLabel,
  Checkbox,
  Divider,
  Paper,
  Container,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Business as BusinessIcon,
  Language as LanguageIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../hooks/useAuth';

const LoginPage: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { login } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const [formData, setFormData] = useState({
    username: '',
    password: '',
    rememberMe: false,
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const from = location.state?.from?.pathname || '/dashboard';

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'rememberMe' ? checked : value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      await login(formData.username, formData.password);
      navigate(from, { replace: true });
    } catch (err: any) {
      setError(err.response?.data?.error || t('error'));
    } finally {
      setLoading(false);
    }
  };

  const toggleLanguage = () => {
    const newLang = i18n.language === 'en' ? 'ar' : 'en';
    i18n.changeLanguage(newLang);
    document.documentElement.dir = newLang === 'ar' ? 'rtl' : 'ltr';
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        p: 2,
      }}
    >
      <Container maxWidth="sm">
        <Paper
          elevation={24}
          sx={{
            borderRadius: 3,
            overflow: 'hidden',
            background: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(10px)',
          }}
        >
          {/* Header */}
          <Box
            sx={{
              background: 'linear-gradient(45deg, #1976d2, #42a5f5)',
              color: 'white',
              p: 3,
              textAlign: 'center',
              position: 'relative',
            }}
          >
            <IconButton
              onClick={toggleLanguage}
              sx={{
                position: 'absolute',
                top: 16,
                right: 16,
                color: 'white',
              }}
            >
              <LanguageIcon />
            </IconButton>

            <BusinessIcon sx={{ fontSize: 48, mb: 1 }} />
            <Typography variant="h4" fontWeight="bold" gutterBottom>
              {i18n.language === 'ar' ? 'نظام إدارة الأعمال' : 'Business Management System'}
            </Typography>
            <Typography variant="subtitle1" sx={{ opacity: 0.9 }}>
              {t('login')} - {i18n.language === 'ar' ? 'تسجيل الدخول' : 'Sign In'}
            </Typography>
          </Box>

          {/* Login Form */}
          <CardContent sx={{ p: 4 }}>
            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            <form onSubmit={handleSubmit}>
              <TextField
                fullWidth
                name="username"
                label={t('username')}
                value={formData.username}
                onChange={handleChange}
                margin="normal"
                required
                autoFocus
                autoComplete="username"
                sx={{ mb: 2 }}
              />

              <TextField
                fullWidth
                name="password"
                label={t('password')}
                type={showPassword ? 'text' : 'password'}
                value={formData.password}
                onChange={handleChange}
                margin="normal"
                required
                autoComplete="current-password"
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowPassword(!showPassword)}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
                sx={{ mb: 2 }}
              />

              <FormControlLabel
                control={
                  <Checkbox
                    name="rememberMe"
                    checked={formData.rememberMe}
                    onChange={handleChange}
                    color="primary"
                  />
                }
                label={i18n.language === 'ar' ? 'تذكرني' : 'Remember me'}
                sx={{ mb: 3 }}
              />

              <Button
                type="submit"
                fullWidth
                variant="contained"
                size="large"
                loading={loading}
                disabled={loading}
                sx={{
                  py: 1.5,
                  fontSize: '1.1rem',
                  fontWeight: 'bold',
                  background: 'linear-gradient(45deg, #1976d2, #42a5f5)',
                  '&:hover': {
                    background: 'linear-gradient(45deg, #1565c0, #1976d2)',
                  },
                }}
              >
                {loading ? t('loading') : t('login')}
              </Button>
            </form>

            <Divider sx={{ my: 3 }}>
              <Typography variant="body2" color="text.secondary">
                {i18n.language === 'ar' ? 'أو' : 'OR'}
              </Typography>
            </Divider>

            <Box textAlign="center">
              <Button
                variant="text"
                color="primary"
                sx={{ textTransform: 'none' }}
              >
                {t('forgotPassword')}
              </Button>
            </Box>
          </CardContent>

          {/* Footer */}
          <Box
            sx={{
              p: 2,
              backgroundColor: 'grey.50',
              textAlign: 'center',
              borderTop: '1px solid',
              borderColor: 'divider',
            }}
          >
            <Typography variant="caption" color="text.secondary">
              {i18n.language === 'ar' 
                ? 'نظام إدارة الأعمال المتكامل - جميع الحقوق محفوظة'
                : 'Integrated Business Management System - All Rights Reserved'
              }
            </Typography>
          </Box>
        </Paper>

        {/* Demo Credentials */}
        <Card sx={{ mt: 2, backgroundColor: 'rgba(255, 255, 255, 0.9)' }}>
          <CardContent sx={{ p: 2 }}>
            <Typography variant="subtitle2" gutterBottom color="primary">
              {i18n.language === 'ar' ? 'بيانات تجريبية:' : 'Demo Credentials:'}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              <strong>{t('username')}:</strong> admin<br />
              <strong>{t('password')}:</strong> admin123
            </Typography>
          </CardContent>
        </Card>
      </Container>
    </Box>
  );
};

export default LoginPage;
