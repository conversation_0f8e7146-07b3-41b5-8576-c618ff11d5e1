{"name": "business-management-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.19", "@mui/material": "^5.14.20", "@mui/x-charts": "^6.18.1", "@mui/x-data-grid": "^6.18.1", "@mui/x-date-pickers": "^6.18.1", "axios": "^1.6.2", "chart.js": "^4.4.0", "date-fns": "^2.30.0", "i18next": "^23.7.6", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-i18next": "^13.5.0", "react-router-dom": "^6.20.1", "recharts": "^2.8.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.2.2", "vite": "^5.0.0"}}