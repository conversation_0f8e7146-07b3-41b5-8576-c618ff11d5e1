# Installation Guide - نظام إدارة الأعمال المتكامل

## System Requirements - متطلبات النظام

### Minimum Requirements - الحد الأدنى للمتطلبات
- **Operating System**: Windows 10, macOS 10.15, or Linux Ubuntu 18.04+
- **Python**: 3.8 or higher
- **Node.js**: 16.0 or higher
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 10GB free space
- **Browser**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+

### Recommended Requirements - المتطلبات المُوصى بها
- **RAM**: 8GB or more
- **Storage**: SSD with 20GB+ free space
- **CPU**: Multi-core processor
- **Network**: Stable internet connection for updates

## Quick Installation - التثبيت السريع

### Automated Setup (Recommended) - الإعداد التلقائي (مُوصى به)

1. **Download and extract the project files**
   ```bash
   # Clone or download the project
   git clone <repository-url>
   cd business-management-system
   ```

2. **Run the setup script**
   ```bash
   # On Windows
   python setup.py
   
   # On Linux/Mac
   python3 setup.py
   ```

3. **Start the application**
   ```bash
   # On Windows
   start.bat
   
   # On Linux/Mac
   ./start.sh
   ```

4. **Access the application**
   - Open your browser and go to: http://localhost:3000
   - Login with: `admin` / `admin123`

## Manual Installation - التثبيت اليدوي

### Step 1: Install Prerequisites - الخطوة 1: تثبيت المتطلبات الأساسية

#### Python Installation
- **Windows**: Download from https://python.org
- **macOS**: `brew install python3` or download from python.org
- **Linux**: `sudo apt-get install python3 python3-pip python3-venv`

#### Node.js Installation
- **All platforms**: Download from https://nodejs.org
- **macOS**: `brew install node`
- **Linux**: `sudo apt-get install nodejs npm`

### Step 2: Backend Setup - الخطوة 2: إعداد الخادم الخلفي

1. **Navigate to backend directory**
   ```bash
   cd backend
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   
   # Activate virtual environment
   # Windows:
   venv\Scripts\activate
   
   # Linux/Mac:
   source venv/bin/activate
   ```

3. **Install Python dependencies**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

4. **Initialize database**
   ```bash
   python -c "from app import create_app, db; app = create_app(); app.app_context().push(); db.create_all(); print('Database initialized')"
   ```

5. **Start backend server**
   ```bash
   python app.py
   ```

### Step 3: Frontend Setup - الخطوة 3: إعداد الواجهة الأمامية

1. **Open new terminal and navigate to frontend directory**
   ```bash
   cd frontend
   ```

2. **Install Node.js dependencies**
   ```bash
   npm install
   ```

3. **Start frontend development server**
   ```bash
   npm run dev
   ```

### Step 4: Access Application - الخطوة 4: الوصول للتطبيق

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000
- **Default Login**: admin / admin123

## Configuration - التكوين

### Environment Variables - متغيرات البيئة

Create a `.env` file in the `backend` directory:

```env
# Flask Configuration
FLASK_ENV=development
SECRET_KEY=your-secret-key-change-in-production
JWT_SECRET_KEY=jwt-secret-string-change-in-production

# Database Configuration
DATABASE_URL=sqlite:///database.db

# Email Configuration (Optional)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_DEFAULT_SENDER=<EMAIL>

# Company Information
COMPANY_NAME=Your Company Name
DEFAULT_CURRENCY=USD
DEFAULT_LANGUAGE=en
```

### Database Configuration - تكوين قاعدة البيانات

#### SQLite (Default) - SQLite (افتراضي)
- No additional configuration needed
- Database file: `backend/database.db`

#### PostgreSQL (Production) - PostgreSQL (للإنتاج)
```env
DATABASE_URL=postgresql://username:password@localhost:5432/business_db
```

#### MySQL (Alternative) - MySQL (بديل)
```env
DATABASE_URL=mysql://username:password@localhost:3306/business_db
```

## Troubleshooting - استكشاف الأخطاء وإصلاحها

### Common Issues - المشاكل الشائعة

#### Port Already in Use - المنفذ مُستخدم بالفعل
```bash
# Find process using port 5000
netstat -ano | findstr :5000  # Windows
lsof -i :5000                 # Linux/Mac

# Kill process
taskkill /PID <PID> /F        # Windows
kill -9 <PID>                 # Linux/Mac
```

#### Python Virtual Environment Issues - مشاكل البيئة الافتراضية لـ Python
```bash
# Remove and recreate virtual environment
rm -rf venv  # Linux/Mac
rmdir /s venv  # Windows

python -m venv venv
```

#### Node.js Dependencies Issues - مشاكل تبعيات Node.js
```bash
# Clear npm cache and reinstall
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

#### Database Connection Issues - مشاكل الاتصال بقاعدة البيانات
```bash
# Reset database
cd backend
rm database.db
python -c "from app import create_app, db; app = create_app(); app.app_context().push(); db.create_all()"
```

### Performance Optimization - تحسين الأداء

#### Backend Optimization - تحسين الخادم الخلفي
- Use production WSGI server (Gunicorn, uWSGI)
- Enable database connection pooling
- Configure caching (Redis, Memcached)

#### Frontend Optimization - تحسين الواجهة الأمامية
- Build for production: `npm run build`
- Use CDN for static assets
- Enable gzip compression

## Security Considerations - اعتبارات الأمان

### Production Security - أمان الإنتاج
1. **Change default credentials**
2. **Use strong secret keys**
3. **Enable HTTPS**
4. **Configure firewall**
5. **Regular security updates**
6. **Database backups**

### Environment Security - أمان البيئة
```env
# Use strong, unique keys
SECRET_KEY=your-very-long-random-secret-key
JWT_SECRET_KEY=another-very-long-random-jwt-key

# Disable debug in production
FLASK_ENV=production
```

## Backup and Maintenance - النسخ الاحتياطي والصيانة

### Database Backup - النسخ الاحتياطي لقاعدة البيانات
```bash
# SQLite backup
cp backend/database.db backup/database_$(date +%Y%m%d).db

# PostgreSQL backup
pg_dump business_db > backup/business_db_$(date +%Y%m%d).sql
```

### Log Management - إدارة السجلات
- Monitor application logs
- Rotate log files regularly
- Set up log aggregation

## Support - الدعم

### Documentation - التوثيق
- API Documentation: http://localhost:5000/api/docs
- User Manual: Available in `docs/` directory

### Getting Help - الحصول على المساعدة
- Check troubleshooting section
- Review error logs
- Contact technical support

### Updates - التحديثات
```bash
# Update backend dependencies
cd backend
pip install --upgrade -r requirements.txt

# Update frontend dependencies
cd frontend
npm update
```

## Development - التطوير

### Development Mode - وضع التطوير
- Backend: Auto-reload enabled
- Frontend: Hot module replacement
- Debug mode: Enabled

### Testing - الاختبار
```bash
# Backend tests
cd backend
python -m pytest

# Frontend tests
cd frontend
npm test
```

### Building for Production - البناء للإنتاج
```bash
# Frontend production build
cd frontend
npm run build

# Backend production setup
cd backend
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```
