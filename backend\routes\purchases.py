from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt
from app import db
from models.purchase import Purchase

purchases_bp = Blueprint('purchases', __name__)

@purchases_bp.route('/', methods=['GET'])
@jwt_required()
def get_purchases():
    """Get all purchases"""
    try:
        claims = get_jwt()
        branch_id = claims.get('branch_id')
        
        query = Purchase.query
        if branch_id:
            query = query.filter_by(branch_id=branch_id)
        
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        purchases = query.paginate(page=page, per_page=per_page, error_out=False)
        
        return jsonify({
            'purchases': [purchase.to_dict() for purchase in purchases.items],
            'total': purchases.total,
            'pages': purchases.pages,
            'current_page': page
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Add more purchase routes here...
