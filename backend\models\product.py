from app import db
from datetime import datetime

class Product(db.Model):
    __tablename__ = 'products'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(50), unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=False)
    name_ar = db.Column(db.String(100))  # Arabic name
    description = db.Column(db.Text)
    description_ar = db.Column(db.Text)  # Arabic description
    
    # Product details
    category_id = db.Column(db.Integer, db.ForeignKey('product_categories.id'))
    brand = db.Column(db.String(50))
    model = db.Column(db.String(50))
    
    # Pricing
    cost_price = db.Column(db.Numeric(15, 2), nullable=False, default=0)
    selling_price = db.Column(db.Numeric(15, 2), nullable=False, default=0)
    wholesale_price = db.Column(db.Numeric(15, 2), default=0)
    
    # Inventory
    min_stock_level = db.Column(db.Integer, default=0)
    max_stock_level = db.Column(db.Integer, default=0)
    reorder_point = db.Column(db.Integer, default=0)
    
    # Physical properties
    weight = db.Column(db.Numeric(10, 3))  # in kg
    dimensions = db.Column(db.String(50))  # L x W x H
    color = db.Column(db.String(30))
    size = db.Column(db.String(30))
    
    # Tax and accounting
    tax_rate = db.Column(db.Numeric(5, 2), default=0)
    tax_inclusive = db.Column(db.Boolean, default=False)
    
    # Status and flags
    is_active = db.Column(db.Boolean, default=True)
    is_service = db.Column(db.Boolean, default=False)
    track_inventory = db.Column(db.Boolean, default=True)
    
    # Images and files
    image_url = db.Column(db.String(255))
    barcode = db.Column(db.String(50))
    
    # Branch association
    branch_id = db.Column(db.Integer, db.ForeignKey('branches.id'))
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    branch = db.relationship('Branch', backref='products')
    category = db.relationship('ProductCategory', backref='products')
    inventory = db.relationship('Inventory', backref='product', uselist=False)
    
    def get_current_stock(self):
        """Get current stock quantity"""
        if self.inventory:
            return self.inventory.quantity
        return 0
    
    def calculate_profit_margin(self):
        """Calculate profit margin percentage"""
        if self.cost_price and self.selling_price:
            profit = float(self.selling_price) - float(self.cost_price)
            return (profit / float(self.selling_price)) * 100
        return 0
    
    def is_low_stock(self):
        """Check if product is low on stock"""
        current_stock = self.get_current_stock()
        return current_stock <= self.min_stock_level
    
    def is_out_of_stock(self):
        """Check if product is out of stock"""
        return self.get_current_stock() == 0
    
    def to_dict(self):
        """Convert product object to dictionary"""
        return {
            'id': self.id,
            'code': self.code,
            'name': self.name,
            'name_ar': self.name_ar,
            'description': self.description,
            'description_ar': self.description_ar,
            'category_id': self.category_id,
            'brand': self.brand,
            'model': self.model,
            'cost_price': float(self.cost_price) if self.cost_price else 0,
            'selling_price': float(self.selling_price) if self.selling_price else 0,
            'wholesale_price': float(self.wholesale_price) if self.wholesale_price else 0,
            'min_stock_level': self.min_stock_level,
            'max_stock_level': self.max_stock_level,
            'reorder_point': self.reorder_point,
            'weight': float(self.weight) if self.weight else 0,
            'dimensions': self.dimensions,
            'color': self.color,
            'size': self.size,
            'tax_rate': float(self.tax_rate) if self.tax_rate else 0,
            'tax_inclusive': self.tax_inclusive,
            'is_active': self.is_active,
            'is_service': self.is_service,
            'track_inventory': self.track_inventory,
            'image_url': self.image_url,
            'barcode': self.barcode,
            'branch_id': self.branch_id,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'current_stock': self.get_current_stock(),
            'profit_margin': self.calculate_profit_margin(),
            'is_low_stock': self.is_low_stock(),
            'is_out_of_stock': self.is_out_of_stock()
        }
    
    def __repr__(self):
        return f'<Product {self.name}>'

class ProductCategory(db.Model):
    __tablename__ = 'product_categories'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    name_ar = db.Column(db.String(50))
    description = db.Column(db.Text)
    parent_id = db.Column(db.Integer, db.ForeignKey('product_categories.id'))
    
    # Status
    is_active = db.Column(db.Boolean, default=True)
    
    # Branch association
    branch_id = db.Column(db.Integer, db.ForeignKey('branches.id'))
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    branch = db.relationship('Branch', backref='product_categories')
    parent = db.relationship('ProductCategory', remote_side=[id], backref='children')
    
    def to_dict(self):
        """Convert category object to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'name_ar': self.name_ar,
            'description': self.description,
            'parent_id': self.parent_id,
            'is_active': self.is_active,
            'branch_id': self.branch_id,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def __repr__(self):
        return f'<ProductCategory {self.name}>'
