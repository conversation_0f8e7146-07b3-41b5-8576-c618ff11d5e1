from app import db
from datetime import datetime

class Customer(db.Model):
    __tablename__ = 'customers'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(20), unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=False)
    name_ar = db.Column(db.String(100))  # Arabic name
    
    # Customer type
    customer_type = db.Column(db.String(20), default='individual')  # individual, company
    
    # Contact information
    contact_person = db.Column(db.String(100))
    email = db.Column(db.String(120))
    phone = db.Column(db.String(20))
    mobile = db.Column(db.String(20))
    fax = db.Column(db.String(20))
    website = db.Column(db.String(255))
    
    # Address
    address = db.Column(db.Text)
    city = db.Column(db.String(50))
    state = db.Column(db.String(50))
    postal_code = db.Column(db.String(20))
    country = db.Column(db.String(50))
    
    # Business information (for companies)
    tax_id = db.Column(db.String(50))
    registration_number = db.Column(db.String(50))
    business_type = db.Column(db.String(50))
    industry = db.Column(db.String(50))
    
    # Financial information
    credit_limit = db.Column(db.Numeric(15, 2), default=0)
    payment_terms = db.Column(db.String(50))  # Net 30, Net 60, etc.
    currency = db.Column(db.String(3), default='USD')
    discount_percentage = db.Column(db.Numeric(5, 2), default=0)
    
    # Banking information
    bank_name = db.Column(db.String(100))
    bank_account = db.Column(db.String(50))
    iban = db.Column(db.String(50))
    swift_code = db.Column(db.String(20))
    
    # Customer classification
    category = db.Column(db.String(50))  # VIP, Regular, New, etc.
    source = db.Column(db.String(50))  # How they found us
    assigned_salesperson = db.Column(db.String(100))
    
    # Status and preferences
    status = db.Column(db.String(20), default='active')  # active, inactive, blocked
    is_vip = db.Column(db.Boolean, default=False)
    preferred_language = db.Column(db.String(5), default='en')
    
    # Additional information
    notes = db.Column(db.Text)
    tags = db.Column(db.String(255))  # Comma-separated tags
    
    # Branch association
    branch_id = db.Column(db.Integer, db.ForeignKey('branches.id'))
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_purchase_date = db.Column(db.DateTime)
    
    # Relationships
    branch = db.relationship('Branch', backref='customers')
    sales = db.relationship('Sale', backref='customer', lazy='dynamic')
    invoices = db.relationship('Invoice', backref='customer', lazy='dynamic')
    
    def calculate_total_sales(self):
        """Calculate total sales amount to this customer"""
        total = db.session.query(db.func.sum(Sale.total_amount)).filter_by(customer_id=self.id).scalar()
        return float(total) if total else 0.0
    
    def calculate_outstanding_balance(self):
        """Calculate outstanding balance from this customer"""
        # Calculate from unpaid invoices
        total = db.session.query(db.func.sum(Invoice.total_amount)).filter(
            Invoice.customer_id == self.id,
            Invoice.status.in_(['pending', 'overdue'])
        ).scalar()
        return float(total) if total else 0.0
    
    def get_recent_sales(self, limit=5):
        """Get recent sales to this customer"""
        return self.sales.order_by(Sale.created_at.desc()).limit(limit).all()
    
    def get_payment_history(self, limit=10):
        """Get payment history for this customer"""
        # This would return payment records
        # For now, returning empty list as placeholder
        return []
    
    def calculate_lifetime_value(self):
        """Calculate customer lifetime value"""
        return self.calculate_total_sales()
    
    def get_purchase_frequency(self):
        """Calculate average days between purchases"""
        sales_count = self.sales.count()
        if sales_count < 2:
            return 0
        
        first_sale = self.sales.order_by(Sale.created_at.asc()).first()
        last_sale = self.sales.order_by(Sale.created_at.desc()).first()
        
        if first_sale and last_sale:
            days_diff = (last_sale.created_at - first_sale.created_at).days
            return days_diff / (sales_count - 1) if sales_count > 1 else 0
        
        return 0
    
    def to_dict(self):
        """Convert customer object to dictionary"""
        return {
            'id': self.id,
            'code': self.code,
            'name': self.name,
            'name_ar': self.name_ar,
            'customer_type': self.customer_type,
            'contact_person': self.contact_person,
            'email': self.email,
            'phone': self.phone,
            'mobile': self.mobile,
            'fax': self.fax,
            'website': self.website,
            'address': self.address,
            'city': self.city,
            'state': self.state,
            'postal_code': self.postal_code,
            'country': self.country,
            'tax_id': self.tax_id,
            'registration_number': self.registration_number,
            'business_type': self.business_type,
            'industry': self.industry,
            'credit_limit': float(self.credit_limit) if self.credit_limit else 0,
            'payment_terms': self.payment_terms,
            'currency': self.currency,
            'discount_percentage': float(self.discount_percentage) if self.discount_percentage else 0,
            'bank_name': self.bank_name,
            'bank_account': self.bank_account,
            'iban': self.iban,
            'swift_code': self.swift_code,
            'category': self.category,
            'source': self.source,
            'assigned_salesperson': self.assigned_salesperson,
            'status': self.status,
            'is_vip': self.is_vip,
            'preferred_language': self.preferred_language,
            'notes': self.notes,
            'tags': self.tags,
            'branch_id': self.branch_id,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'last_purchase_date': self.last_purchase_date.isoformat() if self.last_purchase_date else None,
            'total_sales': self.calculate_total_sales(),
            'outstanding_balance': self.calculate_outstanding_balance(),
            'lifetime_value': self.calculate_lifetime_value(),
            'purchase_frequency': self.get_purchase_frequency()
        }
    
    def __repr__(self):
        return f'<Customer {self.name}>'
