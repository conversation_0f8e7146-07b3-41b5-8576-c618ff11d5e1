from app import db
from datetime import datetime, timedelta

class Invoice(db.Model):
    __tablename__ = 'invoices'
    
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    
    # Customer information
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'), nullable=False)
    
    # Invoice details
    invoice_date = db.Column(db.DateTime, default=datetime.utcnow)
    due_date = db.Column(db.DateTime)
    
    # Financial information
    subtotal = db.Column(db.Numeric(15, 2), default=0)
    tax_amount = db.Column(db.Numeric(15, 2), default=0)
    discount_amount = db.Column(db.Numeric(15, 2), default=0)
    total_amount = db.Column(db.Numeric(15, 2), default=0)
    paid_amount = db.Column(db.Numeric(15, 2), default=0)
    balance_due = db.Column(db.Numeric(15, 2), default=0)
    
    # Status
    status = db.Column(db.String(20), default='draft')  # draft, sent, pending, paid, overdue, cancelled
    payment_status = db.Column(db.String(20), default='unpaid')  # unpaid, partial, paid
    
    # Reference information
    reference_number = db.Column(db.String(50))
    sale_id = db.Column(db.Integer, db.ForeignKey('sales.id'))  # Link to sale if applicable
    
    # Terms and conditions
    payment_terms = db.Column(db.String(50))  # Net 30, Net 60, etc.
    terms_and_conditions = db.Column(db.Text)
    
    # Additional information
    notes = db.Column(db.Text)
    internal_notes = db.Column(db.Text)
    
    # File attachments
    pdf_file_path = db.Column(db.String(255))
    
    # Staff information
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # Branch association
    branch_id = db.Column(db.Integer, db.ForeignKey('branches.id'))
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    sent_at = db.Column(db.DateTime)
    
    # Relationships
    branch = db.relationship('Branch', backref='invoices')
    created_by = db.relationship('User', backref='invoices')
    sale = db.relationship('Sale', backref='invoices')
    items = db.relationship('InvoiceItem', backref='invoice', lazy='dynamic', cascade='all, delete-orphan')
    payments = db.relationship('InvoicePayment', backref='invoice', lazy='dynamic', cascade='all, delete-orphan')
    
    def calculate_totals(self):
        """Calculate invoice totals from items"""
        subtotal = sum(item.total_amount for item in self.items)
        self.subtotal = subtotal
        
        # Calculate tax (simplified - could be more complex)
        self.tax_amount = subtotal * 0.1  # 10% tax rate
        
        # Apply discount
        self.total_amount = subtotal + self.tax_amount - self.discount_amount
        self.balance_due = self.total_amount - self.paid_amount
        
        db.session.commit()
    
    def add_payment(self, amount, payment_method='cash', reference=None, notes=None):
        """Add payment to invoice"""
        if amount <= 0:
            return False
        
        # Create payment record
        payment = InvoicePayment(
            invoice_id=self.id,
            amount=amount,
            payment_method=payment_method,
            reference=reference,
            notes=notes,
            branch_id=self.branch_id
        )
        db.session.add(payment)
        
        # Update invoice amounts
        self.paid_amount += amount
        self.balance_due = self.total_amount - self.paid_amount
        
        # Update payment status
        if self.balance_due <= 0:
            self.payment_status = 'paid'
            self.status = 'paid'
        elif self.paid_amount > 0:
            self.payment_status = 'partial'
        
        db.session.commit()
        return True
    
    def check_overdue(self):
        """Check if invoice is overdue and update status"""
        if self.due_date and self.due_date < datetime.utcnow() and self.status in ['sent', 'pending']:
            if self.balance_due > 0:
                self.status = 'overdue'
                db.session.commit()
                return True
        return False
    
    def send_invoice(self):
        """Mark invoice as sent"""
        if self.status == 'draft':
            self.status = 'sent'
            self.sent_at = datetime.utcnow()
            db.session.commit()
            return True
        return False
    
    def generate_pdf(self):
        """Generate PDF version of invoice"""
        # This would use reportlab or similar to generate PDF
        # For now, just return placeholder path
        self.pdf_file_path = f'invoices/invoice_{self.invoice_number}.pdf'
        db.session.commit()
        return self.pdf_file_path
    
    def to_dict(self):
        """Convert invoice object to dictionary"""
        return {
            'id': self.id,
            'invoice_number': self.invoice_number,
            'customer_id': self.customer_id,
            'invoice_date': self.invoice_date.isoformat(),
            'due_date': self.due_date.isoformat() if self.due_date else None,
            'subtotal': float(self.subtotal) if self.subtotal else 0,
            'tax_amount': float(self.tax_amount) if self.tax_amount else 0,
            'discount_amount': float(self.discount_amount) if self.discount_amount else 0,
            'total_amount': float(self.total_amount) if self.total_amount else 0,
            'paid_amount': float(self.paid_amount) if self.paid_amount else 0,
            'balance_due': float(self.balance_due) if self.balance_due else 0,
            'status': self.status,
            'payment_status': self.payment_status,
            'reference_number': self.reference_number,
            'sale_id': self.sale_id,
            'payment_terms': self.payment_terms,
            'terms_and_conditions': self.terms_and_conditions,
            'notes': self.notes,
            'internal_notes': self.internal_notes,
            'pdf_file_path': self.pdf_file_path,
            'created_by_id': self.created_by_id,
            'branch_id': self.branch_id,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'sent_at': self.sent_at.isoformat() if self.sent_at else None,
            'items': [item.to_dict() for item in self.items],
            'payments': [payment.to_dict() for payment in self.payments]
        }
    
    def __repr__(self):
        return f'<Invoice {self.invoice_number}>'

class InvoiceItem(db.Model):
    __tablename__ = 'invoice_items'
    
    id = db.Column(db.Integer, primary_key=True)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'))
    
    # Item details
    description = db.Column(db.String(255), nullable=False)
    product_code = db.Column(db.String(50))
    
    # Quantities and pricing
    quantity = db.Column(db.Integer, nullable=False, default=1)
    unit_price = db.Column(db.Numeric(15, 2), nullable=False)
    discount_percentage = db.Column(db.Numeric(5, 2), default=0)
    discount_amount = db.Column(db.Numeric(15, 2), default=0)
    tax_rate = db.Column(db.Numeric(5, 2), default=0)
    tax_amount = db.Column(db.Numeric(15, 2), default=0)
    total_amount = db.Column(db.Numeric(15, 2), nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    product = db.relationship('Product', backref='invoice_items')
    
    def calculate_total(self):
        """Calculate item total"""
        subtotal = self.quantity * self.unit_price
        discount = subtotal * (self.discount_percentage / 100) if self.discount_percentage else self.discount_amount
        taxable_amount = subtotal - discount
        tax = taxable_amount * (self.tax_rate / 100)
        self.total_amount = taxable_amount + tax
        return self.total_amount
    
    def to_dict(self):
        """Convert invoice item object to dictionary"""
        return {
            'id': self.id,
            'invoice_id': self.invoice_id,
            'product_id': self.product_id,
            'description': self.description,
            'product_code': self.product_code,
            'quantity': self.quantity,
            'unit_price': float(self.unit_price) if self.unit_price else 0,
            'discount_percentage': float(self.discount_percentage) if self.discount_percentage else 0,
            'discount_amount': float(self.discount_amount) if self.discount_amount else 0,
            'tax_rate': float(self.tax_rate) if self.tax_rate else 0,
            'tax_amount': float(self.tax_amount) if self.tax_amount else 0,
            'total_amount': float(self.total_amount) if self.total_amount else 0,
            'created_at': self.created_at.isoformat()
        }
    
    def __repr__(self):
        return f'<InvoiceItem {self.description}: {self.quantity}>'

class InvoicePayment(db.Model):
    __tablename__ = 'invoice_payments'
    
    id = db.Column(db.Integer, primary_key=True)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'), nullable=False)
    
    # Payment details
    amount = db.Column(db.Numeric(15, 2), nullable=False)
    payment_method = db.Column(db.String(50), nullable=False)  # cash, card, bank_transfer, etc.
    reference = db.Column(db.String(100))  # Check number, transaction ID, etc.
    
    # Additional information
    notes = db.Column(db.Text)
    
    # Staff information
    received_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # Branch association
    branch_id = db.Column(db.Integer, db.ForeignKey('branches.id'))
    
    # Timestamp
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    branch = db.relationship('Branch', backref='invoice_payments')
    received_by = db.relationship('User', backref='invoice_payments')
    
    def to_dict(self):
        """Convert payment object to dictionary"""
        return {
            'id': self.id,
            'invoice_id': self.invoice_id,
            'amount': float(self.amount) if self.amount else 0,
            'payment_method': self.payment_method,
            'reference': self.reference,
            'notes': self.notes,
            'received_by_id': self.received_by_id,
            'branch_id': self.branch_id,
            'created_at': self.created_at.isoformat()
        }
    
    def __repr__(self):
        return f'<InvoicePayment {self.amount} for Invoice {self.invoice_id}>'
