import React from 'react';
import { Box, Typography, Paper } from '@mui/material';
import { useTranslation } from 'react-i18next';

const SalesPage: React.FC = () => {
  const { t } = useTranslation();

  return (
    <Box>
      <Typography variant="h4" gutterBottom fontWeight="bold">
        {t('sales')}
      </Typography>
      <Paper sx={{ p: 3 }}>
        <Typography variant="body1">
          Sales management functionality will be implemented here.
        </Typography>
      </Paper>
    </Box>
  );
};

export default SalesPage;
