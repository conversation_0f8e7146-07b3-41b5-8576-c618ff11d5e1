# نظام إدارة الأعمال المتكامل - Integrated Business Management System

## نظرة عامة - Overview

نظام إدارة أعمال شامل يدعم اللغتين العربية والإنجليزية مع واجهة مستخدم حديثة وآمنة.
A comprehensive business management system supporting Arabic and English with a modern, secure user interface.

## المميزات الرئيسية - Key Features

### 1. إدارة المشتريات - Purchase Management
- إنشاء وإدارة طلبات الشراء
- متابعة الموردين والأسعار
- إدارة عقود الشراء

### 2. إدارة الموردين - Supplier Management
- قاعدة بيانات شاملة للموردين
- تقييم الموردين وتاريخ التعاملات
- إدارة العقود والاتفاقيات

### 3. إدارة العملاء - Customer Management
- ملفات العملاء الشاملة
- تاريخ المعاملات والمدفوعات
- إدارة العلاقات مع العملاء

### 4. إدارة المبيعات - Sales Management
- نظام نقاط البيع
- إدارة العروض والخصومات
- تتبع الأهداف والإنجازات

### 5. إدارة المخزون - Inventory Management
- تتبع المخزون في الوقت الفعلي
- إدارة المستودعات المتعددة
- تنبيهات نفاد المخزون

### 6. إدارة الحسابات العامة - General Accounting
- دليل الحسابات
- القيود المحاسبية
- الميزان العام

### 7. التقارير المالية - Financial Reports
- قائمة الأرباح والخسائر
- الميزانية العمومية
- التقارير الضريبية

### 8. إدارة الفواتير - Invoice Management
- إنشاء وطباعة الفواتير
- أرشفة الفواتير الإلكترونية
- تتبع المدفوعات

### 9. إدارة الموظفين - Employee Management
- ملفات الموظفين
- إدارة الرواتب والحضور
- تقييم الأداء

### 10. تعدد المستخدمين والصلاحيات - Multi-user & Permissions
- نظام صلاحيات متقدم
- أدوار مخصصة
- تحكم في الوصول

### 11. شاشة دخول آمن - Secure Login
- مصادقة ثنائية العامل
- تشفير كلمات المرور
- جلسات آمنة

### 12. نظام الإشعارات - Notification System
- إشعارات فورية
- تنبيهات البريد الإلكتروني
- إشعارات الهاتف المحمول

### 13. سجل النشاطات - Audit Log
- تتبع جميع العمليات
- سجل التغييرات
- تقارير الأمان

### 14. النسخ الاحتياطي - Backup & Restore
- نسخ احتياطي تلقائي
- استعادة البيانات
- حماية من فقدان البيانات

### 15. الإعدادات العامة - System Settings
- إعدادات العملة
- معدلات الضرائب
- شعار الشركة

### 16. تعدد الفروع - Multi-branch Support
- إدارة فروع متعددة
- تقارير موحدة
- صلاحيات حسب الفرع

### 17. لوحة التحكم - Dashboard
- مؤشرات الأداء الرئيسية
- الرسوم البيانية التفاعلية
- التحديثات الفورية

## التقنيات المستخدمة - Technologies Used

### Frontend
- React 18 with TypeScript
- Material-UI (MUI) for components
- React Router for navigation
- i18next for internationalization
- Chart.js for data visualization
- Axios for API calls

### Backend
- Python Flask
- SQLAlchemy ORM
- Flask-JWT-Extended for authentication
- Flask-CORS for cross-origin requests
- SQLite3 database
- Werkzeug for security

### Development Tools
- Vite for build tooling
- ESLint and Prettier for code quality
- Jest for testing

## متطلبات التشغيل - System Requirements

- Python 3.8+
- Node.js 16+
- Modern web browser
- 4GB RAM minimum
- 10GB storage space

## التثبيت والتشغيل - Installation & Setup

### Backend Setup
```bash
cd backend
pip install -r requirements.txt
python app.py
```

### Frontend Setup
```bash
cd frontend
npm install
npm run dev
```

## الأمان - Security Features

- JWT token authentication
- Password hashing with bcrypt
- CORS protection
- SQL injection prevention
- XSS protection
- CSRF protection

## الدعم والصيانة - Support & Maintenance

- Regular security updates
- Database optimization
- Performance monitoring
- User training materials
- Technical documentation

## الترخيص - License

Proprietary software - All rights reserved

## التواصل - Contact

For support and inquiries, please contact the development team.
